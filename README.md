# 运行和打包环境搭建（2025.02.22）
Windows 10
## 安装32位Nodjs
- 安装14.21.3 32版本（其它版本应该也可以，没有试过）
```bash
$ nvm install v14.21.3 32
$ nvm use v14.21.3 32
```

- 切换npm数据源（建议切换或换其它速度快的源）
``` bash
$ npm config set registry https://registry.npmmirror.com/
```





- 安装Python: 2.7和Visual Studio Build Tools
注意要安装32版本的python2.7和Visual Studio Build Tools，通过下面的npm 可以自动安装
```bash
  # 以管理员身份运行
  $ npm install --global --production windows-build-tools@5.2.2
```

如果安装失败，根据错误提示，手动安装 32位版本的 python2.7.13，再试执行上面的命令
VC安装时间较长，多等待一会

- 安装ATL
Visual Studio Install> Visual Studio 15生成工具2017>单个组件>用于x86和x86的Visual C++ ATL


## 相关环境变量设置
- Electron环境变量设置
  由于目前Electron内核为自编译内核（主要是为了处理pdf打印问题），镜像地址需要设置为OSS地址
  ELECTRON_MIRROR=https://cis-static-common.oss-cn-shanghai.aliyuncs.com/apks/electron/



## 运行
```bash
$ npm install
$ cd third-party\abc-vox-desktop-addon
$ npm install
$ cd ..
$ npm run dev

# 另起命令行窗口运行
$ npm start
```


## 打包相关
### 打包脚本
tools/build32exe.bat    32位打包
tools/build64exe.bat    64位打包
      
打包后exe在 dist目录下

# 业务相关
    
 
 ## native-lib&third-party native代码
 native-lib中的代码文件编码使用utf-8
 1. visual studio 扩展>管理扩展>下载 Force Utf-8(No BOM)
 2. 高级保存选项>选择Unicode  (UTF-8)
 
 
 
## 编译chromium内核并集成
### 编译chromium内核
主要参考以文档：
- https://www.electronjs.org/docs/development/build-instructions-windows
- https://electronjs.org/docs/development/build-instructions-gn

#### 环境变量设置

```
DEPOT_TOOLS_WIN_TOOLCHAIN=0
set CHROMIUM_BUILDTOOLS_PATH=%cd%\buildtools
GIT_CACHE_PATH="${HOME}/.git_cache"
```



#### 获取代码

```
$ mkdir electron && cd electron
#$ gclient config --name "src/electron" --unmanaged https://github.com/electron/electron
$ gclient config --name "src/electron" --unmanaged https://github.com/electron/electron@v22.3.1
$ gclient sync --with_branch_head --with_tags
# 这将需要一段时间，喝杯咖啡休息一下。
```




#### 32位编译

``` bash
$ gn gen out/Release-x86 --args="import(\"//electron/build/args/release.gn\") target_cpu=\"x86\"" 

$ ninja -C out/Release-x86 electron
```
## Test版本
```bash
$ gn gen out/Testing-x86 --args="import(\"//electron/build/args/testing.gn\") target_cpu=\"x86\""
$ ninja -C out/Testing-x86 electron
```

####  生成visual studio 项目
```
$ gn gen out/Release-x86 --ide=vs2022
```

####  更新代码
```bash
$ cd src/electron
$ git pull
$ gclient sync -f
```

#### 打包
以下命令会生成文件: electron/src/out/Release-x86/out/dist.zip

```bash
$ ninja -C out/Release-x86 electron:electron_dist_zip
```


### 集成到项目中
#### 将上述dist.zip文件，按版本重命名：electron-v<version>-win32-ia32.zip
e.g: electron-v22.3.1-win32-ia32.zip
#### 生成 SHASUMS256.txt文件
使用使用生成计算electron-v<version>-win32-ia32.zip的SHA256值，并将其添加到SHASUMS256.txt文件中
```powershell
Get-FileHash -Algorithm SHA256 -Path "electron/src/out/Release-x86/out/dist.zip"
```


#### 编辑 third-party/electron/checksums.json里的对应文件的SHA256值
```json
{
  "electron-v22.3.1-win32-ia32.zip": "SHA256值"
}
```

#### 将electron-v<version>-win32-ia32.zip和SHASUMS256.txt上传到OSS上
注意要上传递到两个文件夹下
- 22.3.1
- v22.3.1
e.g:
https://cis-static-common.oss-cn-shanghai.aliyuncs.com/apks/electron/v22.3.1/electron-v22.3.1-win32-ia32.zip
https://cis-static-common.oss-cn-shanghai.aliyuncs.com/apks/electron/22.3.1/electron-v22.3.1-win32-ia32.zip
- 