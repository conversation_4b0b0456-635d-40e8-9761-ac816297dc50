<?xml version="1.0" encoding="UTF-8"?>
<Window size="450,300" caption="0,0,0,35">
	<TextColor name="white" value="#ffffffff"/>
	<TextColor name="red" value="#ffff0000"/>
	<TextColor name="blue" value="#ff0000ff"/>
	<TextColor name="green" value="#ff00ff00"/>
	<TextColor name="textdefaultcolor" value="#ff333333"/>
	<TextColor name="darkcolor" value="#ff333333"/>
	<TextColor name="textdefaultdisablecolor" value="#ffa1aebc"/>
	<TextColor name="darkcolor" value="#ffa1aebc"/>

	<Class name="simpleInput" margin="0,3" padding="6,6,6" multiline="false" autohscroll="true" wantreturnmsg="true" wanttab="false" rich="false" normaltextcolor="darkcolor" disabledtextcolor="textdefaultdisablecolor"  font="inner_font_system_14" promptcolor="textdefaultdisablecolor"/>
	<VBox bkcolor="white" >
		<HBox width="auto" height="auto" halign="left" margin="0 24 0 12">
			<Label text="资源路径" font="inner_font_system_14" width="80"/>
			<RichEdit  class="simpleInput" name="resRootDir" height="30" width="300"  promptmode="true"   prompttext="Single line text control"/>
		</HBox>
		<HBox width="auto" height="auto" halign="left" margin="0 24 0 12">
			<Label text="xml名" font="inner_font_system_14" width="80"/>
			<RichEdit class="simpleInput"  name="resXmlName" height="30" width="300"  promptmode="true"  prompttext="Single line text control"/>
		</HBox>
		<Button name="reloadConfigBtn" width="80" height="30" text="重新加载配置" normalcolor ="darkcolor"  pushedcolor="white" font="inner_font_system_14"/>
		
		<Button name="refresh" width="80" height="30" text="刷新" normalcolor ="darkcolor"  pushedcolor="white" font="inner_font_system_14" margin="0 10 0 0"/>
	</VBox>
</Window>