!define INSTALL_DIR_NAME "abcyun-clinic"
!define ABC_PRODUCT_NAME "ABCClinicDesktop"
!define TOP_DIR "C:\Bytestream"
!macro preInit
  	; This macro is inserted at the beginning of the NSIS .OnInit callback
 	SetRegView 64
 	WriteRegExpandStr HKLM "${INSTALL_REGISTRY_KEY}" InstallLocation "${TOP_DIR}\${INSTALL_DIR_NAME}"
 	WriteRegExpandStr HKCU "${INSTALL_REGISTRY_KEY}" InstallLocation "${TOP_DIR}\${INSTALL_DIR_NAME}"

 	SetRegView 32
 	WriteRegExpandStr HKLM "${INSTALL_REGISTRY_KEY}" InstallLocation "${TOP_DIR}\${INSTALL_DIR_NAME}"
 	WriteRegExpandStr HKCU "${INSTALL_REGISTRY_KEY}" InstallLocation "${TOP_DIR}\${INSTALL_DIR_NAME}"
!macroend




; StrContains
; This function does a case sensitive searches for an occurrence of a substring in a string.
; It returns the substring if it is found.
; Otherwise it returns null("").
; Written by kenglish_hi
; Adapted from StrReplace written by dandaman32


Var STR_HAYSTACK
Var STR_NEEDLE
Var STR_CONTAINS_VAR_1
Var STR_CONTAINS_VAR_2
Var STR_CONTAINS_VAR_3
Var STR_CONTAINS_VAR_4
Var STR_RETURN_VAR

Function StrContains
  Exch $STR_NEEDLE
  Exch 1
  Exch $STR_HAYSTACK
  ; Uncomment to debug
  ;MessageBox MB_OK 'STR_NEEDLE = $STR_NEEDLE STR_HAYSTACK = $STR_HAYSTACK '
    StrCpy $STR_RETURN_VAR ""
    StrCpy $STR_CONTAINS_VAR_1 -1
    StrLen $STR_CONTAINS_VAR_2 $STR_NEEDLE
    StrLen $STR_CONTAINS_VAR_4 $STR_HAYSTACK
    loop:
      IntOp $STR_CONTAINS_VAR_1 $STR_CONTAINS_VAR_1 + 1
      StrCpy $STR_CONTAINS_VAR_3 $STR_HAYSTACK $STR_CONTAINS_VAR_2 $STR_CONTAINS_VAR_1
      StrCmp $STR_CONTAINS_VAR_3 $STR_NEEDLE found
      StrCmp $STR_CONTAINS_VAR_1 $STR_CONTAINS_VAR_4 done
      Goto loop
    found:
      StrCpy $STR_RETURN_VAR $STR_NEEDLE
      Goto done
    done:
   Pop $STR_NEEDLE ;Prevent "invalid opcode" errors and keep the
   Exch $STR_RETURN_VAR
FunctionEnd

!macro _StrContainsConstructor OUT NEEDLE HAYSTACK
  Push `${HAYSTACK}`
  Push `${NEEDLE}`
  Call StrContains
  Pop `${OUT}`
!macroend

!define StrContains '!insertmacro "_StrContainsConstructor"'
