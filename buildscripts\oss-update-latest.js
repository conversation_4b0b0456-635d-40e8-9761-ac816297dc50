const path = require('path');
const webpack = require('webpack');
const WebpackAliOSSPlugin = require('webpack-oss')
const {getCLIOption} = require('./utils');
const axios = require('axios');
const {CryptoUtils, FileUtils} = require('../dist/src/sub-project/common');

const packageJson = require("../package.json");
const accessKeyId = "LTAI5t8jWB7k484hkfNg5y9r";
const accessKeySecret = "******************************";
const region = "oss-cn-shanghai";
const bucket = "cis-static-common";

const isArch64 = getCLIOption('arch') === 'x64';
const updatePluginInfo = getCLIOption('updatePluginInfo') !== 'false';
const appName = `abcyun-desktop-win-${isArch64 ? 'x64-' : ''}latest.exe`;
const printServiceName = `abcyun-desktop-win-print-service-${isArch64 ? 'x64-' : ''}latest.exe`;

const API_ACCOUNT_MOBILE = process.env["API_ACCOUNT_MOBILE"].trim();
const API_ACCOUNT_PASSWORD = process.env["API_ACCOUNT_PASSWORD"].trim();
if (!API_ACCOUNT_MOBILE || !API_ACCOUNT_PASSWORD) {
    throw Error(`环境变量API_ACCOUNT_PASSWORD或API_ACCOUNT_MOBILE值为空`);
}


const plugins = [new webpack.NamedModulesPlugin()];
plugins.push(new WebpackAliOSSPlugin({
    accessKeyId: accessKeyId,
    accessKeySecret: accessKeySecret,
    region: region,
    bucket: bucket,
    prefix: `apks/abc_pc`,
    deleteAll: false,	  // 优先匹配format配置项
    local: true,   // 上传打包输出目录里的文件,
    exclude: [{
        test: function (name) {
            const baseName = path.basename(name);
            return baseName !== appName && baseName !== printServiceName;
        }
    }], // 或者 /.*\.html$/,排除.html文件的上传
    output: path.resolve(__dirname, '../dist')
}));


const kApiHost = 'abcyun.cn';

/**
 *
 * @returns {Promise<{token: string,employeeId:string}>}
 */
async function getToken() {
    const url = `https://${kApiHost}/api/v2/mobile/usercenter/login?t=${new Date().getTime()}`;
    console.log(`url = ${url}`);
    const rsp = await axios.post(url,
        {
            mobilePhoneNumber: API_ACCOUNT_MOBILE,
            mobilePassword: API_ACCOUNT_PASSWORD,
            loginType: 2,
            rememberLogin: false
        },
        {
            'Content-Type': "application/json"
        }).catch(error => {
        throw new Error(JSON.stringify(error.response.data));
    });

    console.log(`login token=${JSON.stringify(rsp.data.data)}`);

    const data = rsp.data.data;
    return {
        token: data.token,
        employeeId: data.employee.id
    };
}

async function updateConfig(config) {
    const token = await getToken();
    const url = `https://${kApiHost}/api/v2/shebao/exe-lib/upgrade?employeeId=${token.employeeId}&t=${new Date().getTime()}`;

    console.log(`updateConfig url = ${url}`);
    const rsp = await axios.post(url,
        config,
        {
            headers: {
                'Content-Type': "application/json",
                'Cookie': `_abcyun_token_=${token.token};`
            }
        }).catch(error => {
        throw new Error(JSON.stringify(error.response.data));
    });

    console.log(`updateConfig result=${JSON.stringify(rsp.data)}`);

    return rsp.status === 200;

}

class UpdateUpgradeInfoPlugin {
    apply(compiler) {
        compiler.hooks.done.tapAsync("UpdateUpgradeInfoPlugin", async (compilation, callback) => {
            console.log(`compilation.errors = ${compilation.errors}`);
            const url = `https://cis-static-common.oss-cn-shanghai.aliyuncs.com/apks/abc_pc/${appName}`;
            console.log(`oss uploadUrl = ${url}`);
            const file = path.resolve(__dirname, `../dist/${appName}`);
            const md5 = await CryptoUtils.fileMd5(file);
            const rsp = await updateConfig({
                name: `abc_appdesktop${isArch64 ? '_64' : ''}`,
                version: packageJson.version,
                url: url,
                md5: md5,
                description: packageJson.build.releaseInfo.releaseNotes,
                title: `ABC数字医疗云客户端${isArch64 ? '_64' : ''}`,
                isPrimaryShebaoExe: 0,
                fileSize: FileUtils.fileSizeSync(file),
            });

            console.log(`rsp = ${JSON.stringify(rsp.data)}`);
            callback();
        });
    }
}

if (updatePluginInfo)
    plugins.push(new UpdateUpgradeInfoPlugin());

module.exports = {
    entry: "./buildscripts/dummy.js",
    mode: 'production',
    bail: true,
    output: {
        library: 'hippyReactBase',
    },
    plugins: plugins
};
