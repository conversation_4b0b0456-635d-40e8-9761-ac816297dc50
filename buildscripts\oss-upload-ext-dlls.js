const path = require('path');
const webpack = require('webpack');
const WebpackAliOSSPlugin = require('webpack-oss')
const {ossConfig} = require('./oss-config')
const {getCLIOption} = require('../dist/src/sub-project/conf/clisettings');



let uploadTarget = getCLIOption("uploadTarget");
//如果没有指定构建目标，则从环境变量中读取
if (!uploadTarget)
    uploadTarget = process.env['BUILD_ENV'];

uploadTarget = uploadTarget.trim();

const {accessKeyId, accessKeySecret, region, bucket} = ossConfig;

const plugins = [new webpack.NamedModulesPlugin()];
plugins.push(
    new WebpackAliOSSPlugin({
        accessKeyId: accessKeyId,
        accessKeySecret: accessKeySecret,
        region: region,
        bucket: bucket,
        prefix: `apks/abc_pc_upgrade/ext-dlls/${uploadTarget}`,
        deleteAll: false,	  // 优先匹配format配置项
        local: true,   // 上传打包输出目录里的文件,
        output: path.resolve(__dirname, '../dist/ext-dlls/'),
        exclude: [{
            test: function (name) {
                const baseName = path.basename(name);
                return !baseName.endsWith(".zip") && baseName !== "latest.json";
            }
        }], // 或者 /.*\.html$/,排除.html文件的上传
    }));

module.exports = {
    entry: "./buildscripts/dummy.js",
    mode: 'production',
    bail: true,
    output: {
        library: 'hippyReactBase',
    },
    plugins: plugins
};
