const path = require('path');
const webpack = require('webpack');
const WebpackAliOSSPlugin = require('webpack-oss')
const {getCLIOption} = require('./utils');

let uploadTarget = getCLIOption("uploadTarget");
const uploadJsBundle = getCLIOption("jsbundle") === 'true';
const uploadAppExe = getCLIOption("appExe") === 'true';
let arch = getCLIOption("arch");
//如果没有指定构建目标，则从环境变量中读取
if (!uploadTarget)
    uploadTarget = process.env['BUILD_ENV'];

uploadTarget = uploadTarget.trim();


console.log(`uploadTarget =${uploadTarget}, uploadJsBundle = ${uploadJsBundle}, uploadAppExe=${uploadAppExe}`);
if (uploadTarget !== "dev" && uploadTarget !== "test" && uploadTarget !== "release") {
    throw `不支持的target: ${uploadTarget}`;
}

if (!arch) {
    arch = '32'
}
console.log(`arch = ${arch}`);


const packageJson = require("../package.json");
const appName = `abcyun-desktop-win-${packageJson.version}.exe`;
console.log(`appName = ${appName}`);
const accessKeyId = "LTAI5t8jWB7k484hkfNg5y9r";
const accessKeySecret = "******************************";
const region = "oss-cn-shanghai";
const bucket = "cis-static-common";


const plugins = [new webpack.NamedModulesPlugin()];
if (uploadAppExe) {
    plugins.push(new WebpackAliOSSPlugin({
        accessKeyId: accessKeyId,
        accessKeySecret: accessKeySecret,
        region: region,
        bucket: bucket,
        prefix: `apks/abc_pc_upgrade/app/${uploadTarget}/${arch}`,
        deleteAll: false,	  // 优先匹配format配置项
        local: true,   // 上传打包输出目录里的文件,
        exclude: [{
            test: function (name) {
                const baseName = path.basename(name);
                return baseName !== appName && baseName !== "latest.yml";
            }
        }], // 或者 /.*\.html$/,排除.html文件的上传
        output: path.resolve(__dirname, '../dist')
    }));
}

if (uploadJsBundle) {
    plugins.push(
        new WebpackAliOSSPlugin({
            accessKeyId: accessKeyId,
            accessKeySecret: accessKeySecret,
            region: region,
            bucket: bucket,
            prefix: `apks/abc_pc_upgrade/asar_plugins/${uploadTarget}/${arch}`,
            deleteAll: false,	  // 优先匹配format配置项
            local: true,   // 上传打包输出目录里的文件,
            output: path.resolve(__dirname, '../dist/for-hot-update/')
        }));
}

module.exports = {
    entry: "./buildscripts/dummy.js",
    mode: 'production',
    bail: true,
    output: {
        library: 'hippyReactBase',
    },
    plugins: plugins
};
