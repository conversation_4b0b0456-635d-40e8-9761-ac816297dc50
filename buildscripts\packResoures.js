const {getCLIOption} = require('../dist/src/sub-project/conf/clisettings');

async function main() {
    const fs = require('fs');
    const {CryptoUtils, packageJson} = require('../dist/src/sub-project/common');
    let isArch64 = getCLIOption("arch") === '64';
    console.log(`isArch64 = ${isArch64}`);

    const outputDir = "./dist/for-hot-update";
    const srcPath = `./dist/${isArch64 ? "win-unpacked" : "win-ia32-unpacked"}/resources`;


    const targetZipName = `resources-latest.zip`;
    const destZip = `${outputDir}/${targetZipName}`;


    if (fs.existsSync(outputDir)) {
        fs.rmdirSync(outputDir, {recursive: true});
    }

    fs.mkdirSync(outputDir);

    console.log(`正在生成:${destZip} from ${srcPath}`);

    const AdmZip = require('adm-zip');
    const zip = new AdmZip();

    // add local file
    zip.addLocalFolder(srcPath);
    // or write everything to disk
    zip.writeZip(destZip);

    console.log(`开始计算${destZip} md5...`);
    const md5 = await CryptoUtils.fileMd5(destZip);
    const exeMD5 = await CryptoUtils.fileMd5(`./dist/abcyun-desktop-win-${packageJson.version}.exe`);
    console.log(`${destZip} md5:${md5}, exeMD5=${exeMD5}`);


    const metadata = {
        md5: md5,
        fileName: targetZipName,
        version: packageJson.version,
        minVersion: packageJson.asarUpdateMinVersion,
        releaseInfo: packageJson.build.releaseInfo,
    }

    fs.writeFileSync(`${outputDir}/latest.json`, JSON.stringify(metadata));
    fs.appendFileSync(`./dist/latest.yml`, `md5: ${exeMD5}\n`);
}


main().then();
