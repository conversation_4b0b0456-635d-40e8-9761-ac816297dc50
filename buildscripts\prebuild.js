/**
 *
 * @type {[]}
 */

const fs = require('fs');
const path = require('path');

function copyFolderSync(from, to, exclude/**@type {string[]}*/) {
    if (!fs.existsSync(to))
        fs.mkdirSync(to, {recursive: true});

    fs.readdirSync(from).forEach(element => {
        const file = path.join(from, element);
        if (fs.lstatSync(file).isFile()) {
            if (!exclude.some(item => file.endsWith(item)))
                fs.copyFileSync(file, path.join(to, element));
        } else {
            copyFolderSync(path.join(from, element), path.join(to, element), exclude);
        }
    });
}

const dirs = [
    "resources",
];
dirs.forEach(item => {
    copyFolderSync(item, `dist/${item}`, ["ts", "vue"]);
});

