function forEachArg(argv/**: string[]*/, callback/*: (key: string, value?: string) => boolean | void*/) {
    const PREFIX = "--";
    for (let arg of (argv ?? process.argv)) {
        if (!arg.startsWith(PREFIX)) continue;
        arg = arg.substr(PREFIX.length);
        const tokens = arg.split("=");
        let key;
        let value = null;
        if (tokens.length >= 2) {
            key = tokens[0];
            value = tokens.slice(1).join('=');
        } else {
            key = arg;
        }

        if (callback(key, value) === false) return;
    }

}


function getCLIOption(key/*: string*/, defaultValue/*: any*/) {
    let finalValue = defaultValue;
    forEachArg(process.argv, (key1, value) => {
        if (key === key1) {
            finalValue = value;
            return false;
        }
    });

    return finalValue;
}

module.exports = {
    getCLIOption
}