#"dev" | "test" | "prod"
env=prod

# ABC系统地址入口
entryUrl=abcyun://www.abcyun.cn/login

#插件自动更新
pluginAutoUpdate=true
# 更新
enableUpdateApp=true
enableAutoUpdateJS=true

# 是否支持右键打开调试
enableDebugContextMenu=false

# 调试模式
debug=false

# 启动时重启打印服务
restartPrintSpoolerWhenLaunch=false

# 默认全屏
initialFullscreen=false

# 当render进程异常后，重启客户端
restartWhenRenderProcessError=true


# 客户端开启的监听端口号，用于一些子程序与客户端程序通信使用（如截图程序)
ipcChannelPort=8421


# 客户端以shell方式调起 child-process时 stdio:ignore 选项
childProcessStdioIgnoreWhenShell=true


# 开启专网Exe程序的日志输出级别
#privateNetworkAppDebugLogLevel=1

#客户端数据存放自定义路径
#extraAppDataPath=c:\

# ABC后台服务程序相关配置
[abcServer]
# 是否在调用时自动获取焦点，将界面提到前前台，以解决部分场景下出来密码框不能自动显示的问题
# 部份电脑可能出现二维码扫码界面中输入框无法获取焦点的问题，可尝试关闭此选项
autoFocus=true
# 默认端口
defaultPort=54322


[proxy]
#proxyServers='socks5://**********:8000;socks5://**********:8000'
#proxyBypassList='<local>;localhost.lodop.net'
#proxyCarrier=hangzhou_wasu

# 是否启动ABC网闸模式(USB模式)
proxyAbcGatekeeperEnable = false

# 是否启动ABC网闸模式(网络模式)
proxyAbcGatekeeperNetworkModeEnable = false


# Chromium command --lines
[chromium]
commandLines="--ignore-certificate-errors"
#commandLines="--disable-gpu"

# 社保相关配置
[shebao]
# 专网模式
privateNetworkMode=false

# 在每次刷卡前重置ABC后台服务
resetAbcServer=false

# 社保端进入专网模式，允许联网的其它程序
privateNetworkBypassAppList=

# 社保独立端模式
localMode = false
localPort = 54300
# 社保独立端环境（prod|dev|test）
localModeEnv = prod

# 用于控制前端资源
[frontEndOfflineBundle]
# 是否启用更新
enableUpdateRouteConfig=true
