/**
 * 一些常量定义
 */
const Constants = {
    appName: "ABC数字医疗云",
    abcyunScheme: "abcyun",
    kOfflineBundleAppID: "abcyun-mira-pc"
}

// 0:正式， 1: 预发布, 2:灰度
enum GrayEnv {
    PROD,
    RC,
    GRAY
}

const GrayEnvMap = {
    0: "prod",
    1: "pre",
    2: "gray"
}


const StringToGrayEnvMap = {
    "prod": GrayEnv.PROD,
    "pre": GrayEnv.RC,
    "gray": GrayEnv.GRAY,
    "rc": GrayEnv.RC
}

const kUrlConfig = {
    dev: 'https://dev.abczs.cn',
    test: 'https://test.abczs.cn',
    prod: 'https://www.abcyun.cn',
}


const kProxyRegionHosts = {
    "chongqing": [
        '***********',
        '***********',
        '***********',
    ]
}
export {Constants, GrayEnv, GrayEnvMap, kUrlConfig,StringToGrayEnvMap, kProxyRegionHosts}
