import * as electron from 'electron';
import {app, session} from 'electron';
import {abcConfig} from "./sub-project/conf/abc-conf";
import {sharedPreferences} from "./sub-project/preferences/shared-preferences";
import {logger, NetworkUtils, PathUtils} from "./sub-project/common";
import {abcEnvironment} from "./sub-project/conf/abc-environment";
import {AddonPluginManager} from "./sub-project/addon-plugin-manager/addon-plugin-manager";
import {BuildConfig} from "./build-config";
import path from "path";
import { GrayEnv } from './constants';

const kPluginAddonName = 'abc-vox-desktop-addon';

class AbcApp {
    private _abcDesktopAddon?: any;

    constructor() {
    }


    /**
     * 程序运行入口
     */
    async start() {
        /**
         * 已经启动了一个实例，退出当前
         */
        const gotTheLock = app.requestSingleInstanceLock();
        if (!gotTheLock) {
            app.quit();
            return;
        }

        this._initBeforeElectronAppReady();

        // This method will be called when Electron has finished
        // initialization and is ready to create browser windows.
        // Some APIs can only be used after this event occurs.
        app.on("ready", () => {
            abcApp._initAfterElectronAppReady();
        });
    }


    _initDesktopAddon(first:boolean = true) {
        const currentEnvAddonPluginManager = new AddonPluginManager(abcEnvironment.grayEnv());
        try {
            currentEnvAddonPluginManager.syncPreparePlugin(kPluginAddonName);
            const isPackaged = app.isPackaged;
            // const isPackaged = true;
            let scripts = '';
            let rootDir = '';
            if (isPackaged) {
                scripts = currentEnvAddonPluginManager.getPluginEntryUrl(kPluginAddonName);
                rootDir = path.dirname(scripts);
            }
            else {
                rootDir = path.resolve(PathUtils.getProjectPath(), "../third-party/abc-vox-desktop-addon/dist")
                scripts = path.resolve(rootDir, "src/index.js");
            }

            logger.log(`${kPluginAddonName} try load ${scripts}`);
            this._abcDesktopAddon = require(scripts).default;
            logger.log(`${kPluginAddonName} after require = ${this._abcDesktopAddon}`);

            this._abcDesktopAddon.init({
                isFirstLaunch:abcConfig.isFirstLaunch,
                electron: electron as any,
                version: app.getVersion(),
                networkUtils: NetworkUtils,
                logger: logger,
                abcEnvironment: abcEnvironment,
                pathUtils: PathUtils,
                buildConfig: BuildConfig,
                sharedPreferences: sharedPreferences,
                rootDir: rootDir,
                hostRequire: (module: string) => {
                    return require(module);
                }
            })

            logger.log(`${kPluginAddonName} after init`);
        } catch (e) {
            logger.error(`${kPluginAddonName} error = ${e}, stack = ${e.stack}, first = ${first}`);
            //尝试删除addon后，重新加载
            if (first) {
                currentEnvAddonPluginManager.deletePlugin(kPluginAddonName);
                this._initDesktopAddon(false);
            }
        }
    }
    /**
     * 在 ready事件之前初始化部份
     * @private
     */
    _initBeforeElectronAppReady() {
        this._initLogger();
        this._initAppConfig();
        abcEnvironment.init();
        this._initDesktopAddon(true);
        this._abcDesktopAddon?.onBeforeElectronAppReady();
    }

    /**
     * 部份窗口相关(BrowserWindow)相关操作需要在app.on('ready')事件之后才能调用
     * @private
     */
    async _initAfterElectronAppReady() {
        this._initRemoteModule();
        this._initNetwork();
        // 这里直接更新所有环境的
        // 避免进入到对应环境时addon版本过老
        [
            GrayEnv.PROD,
            GrayEnv.GRAY,
            GrayEnv.RC
        ].forEach((env) => {
            logger.info(`${kPluginAddonName}开始更新addon, env = ${env}`);
            const addonPluginManager = new AddonPluginManager(env);
            addonPluginManager.preparePlugin("abc-vox-desktop-addon", true).then();
        });
        try {
            this._abcDesktopAddon.onAfterElectronAppReady();
        } catch (e) {
            logger.error(`_abcDesktopAddon.onAfterElectronAppReady error = ${e}, stack = ${e.stack}`);
        }
    }

    _initLogger() {
        //electron日志配置
        logger.init();
    }

    private _initRemoteModule() {
        const remoteMain = require('@electron/remote/main');
        remoteMain.initialize();
        app.on('web-contents-created', (event, webContents) => {
            remoteMain.enable(webContents);
        });
    }


    /**
     * abc-conf文件可能存在更新，需要合并
     * @private
     */
    _initAppConfig() {
        abcConfig.checkIfNeedMerge();
    }

    private _initNetwork() {
        //检测是否走代理模式
        const proxyServer = abcEnvironment.networkConfig?.proxyServer;
        if (!proxyServer) return;
        session.defaultSession.setProxy({
            proxyRules: proxyServer,
            proxyBypassRules: abcEnvironment.networkConfig.proxyBypassList,
        }).catch((error: any) => {
            logger.log("abcConfig.proxy.proxyBypassList, error = " + error);
        });
    }
}

if (app.isPackaged !== true)
    require('electron-reload')(PathUtils.getProjectPath() + "/src");

const abcApp = new AbcApp();
(app as any).abcApp = abcApp;
abcApp.start().then();
