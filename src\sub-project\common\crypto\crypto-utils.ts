import {FileUtils} from "../file/file_utils";

export class CryptoUtils {
    /**
     * 计算文件的md5值
     * @param file {string}
     * @return Promise<string>
     */
    static async fileMd5(file: string) {
        return new Promise((resolve, reject) => {
            if (!FileUtils.fileExist(file)) {
                resolve('');
                return;
            }

            const crypto = require('crypto');
            const fs = require('fs');
            const md5Generator = crypto.createHash('md5');
            const fd = fs.createReadStream(file);
            md5Generator.setEncoding("hex");
            fd.pipe(md5Generator);

            fd.on('end', () => {
                md5Generator.end();
                resolve(md5Generator.read());
            });
            fd.on("error", (error: Error) => {
                reject(error);
            });
        })
    }
}

