/**
 *
 * @param timeInMills{number}
 * @returns {Promise<void>}
 */
function delay(timeInMills: number) {
    return new Promise((resolve) => {
        setTimeout(function () {
            resolve(undefined);
        }, timeInMills);
    });
}


function timeoutAction<T>(action: () => T| Promise<T>, timeout: number): Promise<T> {
    return new Promise(async (resolve, reject) => {
        let finished = false;
        (async function() {
            const result = await action();
            if (!finished) {
                finished = true;
                resolve(result);
            }
        })().then(()=>{});

        setTimeout(() => {
            if (!finished) {
                finished =true;
                reject(`timeout : ${timeout}ms`);
            }
        }, timeout);
    });
}

export {delay, timeoutAction}
