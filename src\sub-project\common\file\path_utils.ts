import {FileUtils} from "./file_utils";

const path = require("path");

const logger = require('electron-log');
const fs = require('fs');

function app() {
    const {app} = require('electron');
    return app;
}

export class PathUtils {
    static getAppDataRootDir() {
        return app().getPath('userData');
    }


    /**
     * 插件目录结构
     * -plugins
     *      - dev
     *          -rc
     *              -pluginName
     *          -gray
     *              -pluginName
     *      - prod
     *          -rc
     *              -pluginName
     *
     * 获取插件存放要路径
     */
    static getAppPluginRootDir() {
        return `${this.getAppDataRootDir()}/abc_plugins`;
    }

    static getDataDir() {
        if (!app())
            return ".";

        const path = PathUtils.getAppDataRootDir() + "/data";
        FileUtils.dirExistsSync(path, true);
        return path;
    }

    static getSocialSecurityDir() {
        const socialDir = PathUtils.getDataDir() + '/social_security';
        let exist = FileUtils.dirExistsSync(socialDir, true);
        if (!exist) {
            FileUtils.mkdirSync(socialDir);
            logger.info(`create social dir ${socialDir} failed`);
        }

        return socialDir;
    }


    /**
     * flat resource表示不会打包进assr里,会在安装时作为普通文件解压到安装目录/resources/flat_resources
     * @returns {string}
     */
    static getFlatResource() {
        return path.resolve(PathUtils.getResourcesDir(), 'flat_resources');
    }

    /**
     *
     */

    static getResourcesDir() {
        try {
            if (app().isPackaged) {
                if (process.platform === 'win32') {
                    return `${path.resolve(path.dirname(process.execPath), 'resources')}`;
                } else {
                    return `${path.resolve(path.dirname(process.execPath), '../Resources')}`;
                }
            }
        } catch (ignore: any) {
        }

        return `${process.cwd()}/resources`;
    }


    static getTmpResourcesDir() {
        try {
            if (app().isPackaged) {
                if (process.platform === 'win32') {
                    return `${path.resolve(path.dirname(process.execPath), 'resources_tmp')}`;
                } else {
                    return `${path.resolve(path.dirname(process.execPath), '../Resources_tmp')}`;
                }
            }
        } catch (ignore: any) {
        }

        return `${process.cwd()}/resources_tmp`;
    }

    /**
     * @return {string}
     */
    static getAppCacheDir() {
        return `${PathUtils.getAppDataRootDir()}/Cache`
    }

    /**
     * @returns {string}
     */
    static tmpDir() {
        const tmpDir = `${PathUtils.getAppDataRootDir()}/Tmp`;
        if (!fs.existsSync(tmpDir))
            fs.mkdirSync(tmpDir);

        return tmpDir;
    }

    /**
     *  社保独立端资源所在地址
     * @return {string}
     */
    static shebaoAloneResourceDir() {
        const {app} = require('electron');
        return path.resolve(app.getPath('userData'), './shebao-resource');
    }

    static getProjectPath() {
        const pathName = path.dirname(__dirname);
        return path.resolve(`${pathName}/../../..`);
    }
}


export function pathFix(file: string) {
    // 根据日志查找
    let search = process.platform === "win32" ? /\//g : /\\/g;
    return  file.replace(search, path.sep);
}
