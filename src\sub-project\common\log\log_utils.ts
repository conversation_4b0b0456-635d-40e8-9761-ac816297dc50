import {pathFix} from "../file/path_utils";

const electronLogger = require('electron-log');

class LogUtils {
    _enable = true;
    constructor() {
        this._enable = true;
    }

    enableLog(enable: boolean) {
        this._enable = enable;
    }

    info(...params: any[]) {
        if (this._enable)
            electronLogger.info(...params);
    }

    log(...params: any[]) {
        if (this._enable)
            electronLogger.log(...params);
    }

    warn(...params: any[]) {
        if (this._enable)
            electronLogger.warn(...params);
    }

    error(...params: any[]) {
        electronLogger.error(...params);
    }

    /**
     *  日志输入根目录
     * @param userData {string?}
     */
    init(userData?: string) {
        //日志文件最大值
        electronLogger.transports.file.maxSize = 5 * 1024 * 1024; //5M

        let original_logger_info = electronLogger.info;
        let original_logger_log = electronLogger.log;
        electronLogger.info = function (...msg: any[]) {
            original_logger_info((process as any).type + ':' + msg);
        };

        electronLogger.log = function (...msg: any[]) {
            original_logger_log((process as any).type + ':' + msg);
        };

        electronLogger.transports.console.level = 'silly';
        electronLogger.transports.file.level = 'silly';
        try {
            if (!userData) {
                userData = require('electron').app.getPath('userData');
            }
        } catch (e) {

        }

        if (userData) {
            electronLogger.transports.file.file = pathFix(userData + "\\logs\\log.txt");
        }

        electronLogger.info("App Start!!!");
        console.log("electronLog file = " + electronLogger.transports.file.file);
    }
}

const logger = new LogUtils();
export {logger}
