import {CryptoUtils, FileUtils, logger} from "..";

let NET_SESSION_NAME = "abc-network-session";
let sessionIndex = 0;
const TAG = "NetworkUtils";

export type Session = any; //electron.Session
interface RequestOptions {
    protocol?: "http:" | "https:";
    hostname?: string;
    port?: number;
    path?: string;
    headers?: Record<string, string>;
    session?: Session;
    method?: "GET" | "POST"
}

const changeSessionNameWhenErrFailed = (e: any) => {
    // electron NetworkService crash重启后，之前创建的session无法使用(会报ERR_FAILED)，需要换个名称重新创建
    if (e && e.message === 'net::ERR_FAILED') {
        NET_SESSION_NAME = `abc-network-session-${sessionIndex++}`;
    }
}

class NetworkUtils {
    /**
     *
     * @param url{string}
     * @returns Promise<string>
     */
    static getAsString(url: string): Promise<string> {
        return NetworkUtils.getAsStringWithHeaders(url);
    }


    static async getAsStringAutoCookie(url: string): Promise<string> {
        logger.log(`${TAG}.getAsStringAutoCookie url = ${url}`);
        const electron = require('electron')
        const cookie = (await electron.session.defaultSession.cookies.get({url: url}))?.map(item => `${item.name}=${item.value}`)?.join(';');
        return NetworkUtils.getAsStringWithHeaders(url, {Cookie: cookie});
    }

    /**
     *
     * @param url{string}
     * @param headers {any}
     * @returns Promise<string>
     */
    static getAsStringWithHeaders(url: string, headers?: any): Promise<string> {
        return new Promise((resolve, reject) => {
            const {net} = require('electron');
            const request = net.request(this._createHttpRequest(url));
            if (headers) {
                Object.keys(headers).forEach(key => {
                    request.setHeader(key, headers[key]);
                });
            }

            request.on('response', (res: any) => {
                let result = '';
                res.on('data', (data: any) => {
                    result += data;
                })
                res.on('end', () => {
                    resolve(result);
                })
            }).on('error', (e: Error) => {
                logger.log(`${TAG}.getAsStringWithHeaders error url = ${url}, error = ${e.message}, stack = ${e.stack}`);
                changeSessionNameWhenErrFailed(e);
                reject(e);
            });
            request.end()
        });
    }


    static post(url: string, params: { body: string, headers?: any }): Promise<string> {
        logger.log(`${TAG}.post url = ${url}`);
        return new Promise((resolve, reject) => {
            const {body, headers} = params;
            const {net} = require('electron');
            const request = net.request(this._createHttpRequest(url, "POST"));
            if (headers) {
                Object.keys(headers).forEach(key => {
                    request.setHeader(key, headers[key]);
                });
            }
            request.on('response', (res: any) => {
                let result = '';
                res.on('data', (data: any) => {
                    result += data;
                })
                res.on('end', () => {
                    resolve(result);
                })
            }).on('error', (e: Error) => {
                logger.log(`${TAG}.post error url = ${url}, error = ${e.message}, stack = ${e.stack}`);
                reject(e);
            });

            if (body) {
                request.write(body)
            }

            request.end()
        });
    }

    /**
     *
     * @param options {}}
     *
     * @return Promise<void>
     */
    static downloadFile(options: {
        url: string,
        filePath: string,
        md5: string,
        onProgress?: (currentBytes: number, totalBytes?: number) => void
    }) {
        const {url, filePath, md5, onProgress} = options;
        if (!url) return Promise.reject("url参数缺失");
        if (!filePath) return Promise.reject("filePath参数缺失");

        console.log(`NetworkUtils.downloadFile: ${JSON.stringify(options)}`);
        const fs = require('fs');

        return new Promise(async (resolve, reject) => {
            let finished = false;
            const finish = (error: any) => {
                if (finished)
                    return;

                finished = true;

                if (error) {
                    reject(error);
                } else {
                    resolve(undefined);
                }
            }


            if (fs.existsSync(filePath)) {
                //检查md5，如果一样就不用重新下载了
                const fileMd5 = await CryptoUtils.fileMd5(filePath);
                if (md5 === fileMd5) {
                    resolve(undefined);
                    return;
                }

                fs.unlinkSync(filePath);
            }

            const file = fs.createWriteStream(options.filePath);
            const {net} = require('electron');
            const request = net.request(this._createHttpRequest(url));
            request.on('response', (rsp: any) => {
                rsp.pipe(file);

                const contentLength = parseInt(rsp.headers['content-length']);
                let currentBytes = 0;
                rsp.on('data', (chunk: Buffer) => {
                    currentBytes += chunk.byteLength;
                    onProgress?.(currentBytes, isNaN(contentLength) ? undefined : contentLength);
                });
            });

            request.on("error", (error: any) => {
                file.close();
                changeSessionNameWhenErrFailed(error);
                finish(error?.message ?? error);
            });

            file.on("finish", () => {
                finish(undefined);
            });
            file.on('error', (error: any) => {
                try {
                    fs.unlinkSync(filePath, () => {
                    }); // Delete temp file
                } catch (e) {
                }

                finish(error?.message ?? error);
            });

            request.end();
        }).then(() => {
            if (md5) {
                return CryptoUtils.fileMd5(filePath).then((rsp: any) => {
                    if (md5 !== rsp)
                        throw `downloadFile md5 not match, current md5 = ${rsp}, expected: ${md5}`;
                })
            }
        });
    }


    /**
     * 更新系统host文件，指定host ip映射刘方敏
     * @param hosts
     */
    public static updateHostFile(hosts: Array<{ host: string, ip: string, comment: string }>) {
        // const hosts: Array<{ host: string, ip: string }> = JSON.parse(jsonStr).hosts;
        const hostFile = `C:\\Windows\\System32\\drivers\\etc\\hosts`;
        const fs = require('fs')
        const iconvLite = require('iconv-lite');

        let content = "";
        //读取原始host文件
        if (FileUtils.fileExist(hostFile)) {
            content = iconvLite.decode(fs.readFileSync(hostFile), "GBK") as string;
        }
        logger.log(`更新host配置, 原始内容 = ${content}`);

        //需要设置host映射
        const needSetHostsMap = new Map(hosts.map(item => [item.host, item.ip]));

        const abcComment = "#ABC诊所管家";
        const newLine = "\r\n";
        const existHosts = new Map<string, string>(); //host->ip
        const abcAddedHosts = new Map<string, string>();
        let finalHostContent = content.split(newLine).filter(item => {
            //删除我们自己添加的注释
            if (item.startsWith(abcComment)) return false;

            //其它注释保留
            if (item.startsWith("#")) return true;

            const ipHostPair = item.split(" ").filter(token => token.length > 0);
            const ip = ipHostPair[0];
            const host = ipHostPair[1];
            existHosts.set(host, ip);

            //过滤掉我们添加的项目
            if (item.indexOf(abcComment) > 0) {
                abcAddedHosts.set(host, ip);
                return false;
            }

            //保留不是将要设置的项目
            return !(ipHostPair.length >= 2 && needSetHostsMap.has(ipHostPair[1]));
        }).join(newLine);


        let needUpdate = false;
        //检测已经存在的host映射是否包含了所有要配置项的
        for (const host of needSetHostsMap.keys()) {
            const newValue = needSetHostsMap.get(host);
            const oldValue = existHosts.get(host);
            if (newValue != oldValue) {
                needUpdate = true;
                break;
            }
        }

        if (!needUpdate) {
            //检查是否是有老的配置项要删除
            for (const host of abcAddedHosts.keys()) {
                if (!needSetHostsMap.has(host)) {
                    needUpdate = true;
                    break;
                }
            }
        }

        logger.log(`更新host配置,是否更新检测：${needUpdate}`);
        if (!needUpdate) {
            return;
        }

        finalHostContent += [
            newLine,
            abcComment,

            ...hosts.filter(item => item.ip).map(item => `${item.ip} ${item.host} ${item.comment ? `#${item.comment}` : '#ABC配置'}`)
        ].join(newLine);

        const finalHostContentBuffer = iconvLite.encode(finalHostContent, "GBK");
        fs.writeFileSync(hostFile, finalHostContentBuffer);

        logger.log(`finalHostContent = ${finalHostContent}`);
    }



    private static _createHttpRequest(url: string, method?: "GET" | "POST", headers?: any): RequestOptions {
        return {
            ...this._createRequestOptions(url, headers),
            session: this._getNetSession(),
            method: method ? method : "GET"
        }
    }

    private static _getNetSession(): Session {
        const {session} = require('electron');
        return session.fromPartition(NET_SESSION_NAME, {
            cache: false
        });
    }

    private static _configureRequestUrl(url: URL, options: RequestOptions): void {
        options.protocol = url.protocol as any;
        options.hostname = url.hostname;

        if (url.port) {
            options.port = parseInt(url.port);
        }

        options.path = url.pathname + url.search;
    }

    private static _createRequestOptions(url: string, headers?: any) {
        const result: RequestOptions = {};
        result.headers = headers;
        this._configureRequestUrl(new URL(url), result);
        return result;
    }
}

export {NetworkUtils}
