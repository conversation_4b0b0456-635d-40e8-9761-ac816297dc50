import {logger} from "../log/log_utils";
import {abcEnvironment} from "../../conf/abc-environment";
import {IncomingHttpHeaders} from "http";

/**
 * 使用 nodejs自带的http/s库发送网络请示，解决在electron ready之前 electron自带网络库不能使用的问题
 */

interface RequestOptions {
    protocol: 'http' | 'https',
    host?: string,
    hostname?: string,
    family?: number,
    port: number | string,
    defaultPort?: number | string,
    localAddress?: string,
    socketPath?: string,
    method: string,
    path: string,
    headers?: any/*OutgoingHttpHeaders*/
    body?: any,
    auth?: string,
    tradeRspBodyAsString?: boolean;
    saveResBodyToFileName?: string //文件名,
    useClientProxy?: boolean //是否使用客户端代理
    timeout?:number
}


interface RequestRsp {
    body: Buffer | string;
    aborted: boolean;
    httpVersion: string;
    httpVersionMajor: number;
    httpVersionMinor: number;
    headers: IncomingHttpHeaders;
    // rawHeaders: string[];
    /**
     * Only valid for response obtained from http.ClientRequest.
     */
    statusCode?: number;
    /**
     * Only valid for response obtained from http.ClientRequest.
     */
    statusMessage?: string;
}


export class NetworkWithNodejsHttp {

    /**
     *
     * @param options {{
        protocol: 'https'|'https',
        host: string,
        hostname: string,
        family: number.
        port: number | string,
        defaultPort: number | string,
        localAddress: string,
        socketPath: string,
        method: string,
        path: string,
        headers: OutgoingHttpHeaders,
        auth: string
     }}
     * @returns Promise<string>
     */
    public static request(options: RequestOptions) {
        return this.requestWithDetailRsp({...options, tradeRspBodyAsString: true}).then(rsp => rsp.body);
    }

    public static requestWithDetailRsp(options: RequestOptions): Promise<RequestRsp> {
        return new Promise<RequestRsp>((resolve, reject) => {
            try {
                let optionsStr = JSON.stringify(options);
                logger.log('request:' + optionsStr);
            } catch (e) {
            }

            let {protocol, body, headers, saveResBodyToFileName, useClientProxy, ...nativeOptions} = options;
            let http;
            if (protocol === 'https') {
                http = require('https');
            } else {
                http = require('http');
            }

            if (body) {
                if (!headers) headers = {};
                const contentLength = headers['Content-Length'];
                if (typeof body === 'string' && (contentLength === undefined || contentLength === null))
                    headers['Content-Length'] = Buffer.byteLength(body, "utf8");
            }

            let agent;
            let proxy = abcEnvironment.networkConfig?.proxyServer;

            //使用socks-proxy-agent库需要将socks5替换成socks5h不然，dns不会走proxy仍然使用的是本机的dns
            if (proxy) {
                proxy = proxy.replace("socks5", "socks5h");
            }

            if (useClientProxy && proxy) {
                const { SocksProxyAgent } = require('socks-proxy-agent');
                agent= new SocksProxyAgent(proxy);
            }

            let file;
            if (saveResBodyToFileName) {
                file = require('fs').createWriteStream(saveResBodyToFileName);
                file.on("finish", () => {
                    file.close();
                });
            }
            let req = http.request({...nativeOptions, headers: headers, agent: agent}, (res: any) => {
                let data = [], dataLen = 0;
                if (saveResBodyToFileName) {
                    res.pipe(file);
                } else {
                    res.on("data", (chunk: any) => {
                        data.push(chunk);
                        dataLen += chunk.length;
                    });
                }

                res.on('end', () => {
                    let rspBody: Buffer | string | undefined;
                    if (dataLen) {
                        rspBody = Buffer.alloc(dataLen);
                        for (let i = 0, len = data.length, pos = 0; i < len; i++) {
                            data[i].copy(rspBody, pos);
                            pos += data[i].length;
                        }

                        if (options.tradeRspBodyAsString) {
                            rspBody = rspBody.toString();
                        }
                    }

                    const rsp = {
                        body: rspBody,
                        aborted: res.aborted,
                        httpVersion: res.httpVersion,
                        httpVersionMajor: res.httpVersionMajor,
                        httpVersionMinor: res.httpVersionMajor,
                        headers: res.headers,
                        // rawHeaders: res.rawHeaders,

                        /**
                         * Only valid for response obtained from http.ClientRequest.
                         */
                        statusCode: res.statusCode,
                        /**
                         * Only valid for response obtained from http.ClientRequest.
                         */
                        statusMessage: res.statusMessage,
                    };

                    try {
                        logger.log('request rsp' + JSON.stringify(rsp));
                    } catch (ignored) {
                    }

                    resolve(rsp);
                });
            }).on('error', (e: any) => {
                logger.log('get error', e.message);
                reject(e);
            });

            if (body)
                req.write(body);

            req.end();
        });
    }
}