/**
 * 用于读取abc-conf.ini里配置
 */
import {logger, PathUtils} from "../common";

const fs = require('fs');
const ini = require('ini');
const _ = require('lodash');

const kConfigFileName = "abc-conf.ini";
const kConfigFileNewName = "abc-conf_new.ini";


class AbcConfig {

    //客户端环境
    env: "dev" | "test" | "prod" = "prod";
    pluginAutoUpdate = true;
    entryUrl!: string;
    isFirstLaunch: boolean = false;


    /**
     * @param dataDir {string?} abc-conf.ini文本所在目录
     */
    constructor(dataDir?: string) {
        this.loadWithDir(dataDir);
    }

    /**
     * 从指定目录中加载配置文件
     * @param dataDir {string}
     */
    loadWithDir(dataDir?: string) {
        let config: any = {}
        try {
            try {
                if (!dataDir) {
                    const {app} = require('electron');
                    if (app)
                        dataDir = app.getPath('userData');
                }
                if (dataDir)
                    config = ini.parse(fs.readFileSync(`${dataDir}/${kConfigFileName}`, 'utf-8'));
            } catch (e) {
            }
        } catch (e) {
            console.log(`ABCConfig read abc-conf.ini exception，e = ${e},e =${e.stack}`);
        }


        Object.assign(this, config);
    }

    checkIfNeedMerge(): void {
        logger.log("checkIfNeedMerge");
        const {app} = require('electron');
        if (!app) return;

        const userData = app.getPath('userData');
        if (!userData) return;

        const curConfFile = `${userData}/${kConfigFileName}`;
        try {
            fs.statSync(curConfFile);
        } catch (e) {
            //如果abc-conf.ini文件丢失，则从安装目录下复制一个
            try {
                const configFile = this._getInstallConfFile();
                fs.copyFileSync(configFile, curConfFile);
            } catch (e) {
            }
        }

        try {
            const newConfFile = `${userData}/${kConfigFileNewName}`;
            fs.statSync(newConfFile);
            logger.log(`find file ${kConfigFileNewName}`);
            this.isFirstLaunch = true;
            const newConf = ini.parse(fs.readFileSync(newConfFile, 'utf-8'));

            let currConf;
            try {
                fs.statSync(curConfFile);
                currConf = ini.parse(fs.readFileSync(curConfFile, 'utf-8'));
            } catch (e) {
            }
            if (!currConf)
                currConf = {};

            logger.log("currConf = " + JSON.stringify(currConf));
            logger.log("newConf = " + JSON.stringify(newConf));

            _.merge(newConf, currConf);

            this.loadWithDir();
            logger.log("final newConf = " + JSON.stringify(newConf));
            const newConfStr = ini.encode(newConf);
            fs.writeFileSync(curConfFile, newConfStr);
            fs.unlinkSync(newConfFile);
        } catch (e) {

        }
    }

    private _getInstallConfFile(): string {
        const resourcesDir = PathUtils.getResourcesDir();
        return `${resourcesDir}/conf/${kConfigFileName}`;
    }
}


const abcConfig = new AbcConfig();
export {abcConfig};
