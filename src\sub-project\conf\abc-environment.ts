/**
 * abc 运行环境
 */
import {abcConfig} from "./abc-conf";
import {getCLIOption} from "./clisettings";
import {sharedPreferences} from "../preferences/shared-preferences";
import * as _ from 'lodash';
import {GrayEnv} from "../../constants";

const kLastLoginClinicId = 'last_login_clinic_id';
const kLastRegion = 'last_region';
const kLastGrayEnv = 'last_gray_env';
const kUUID = 'uuid_key';

class AbcEnvironment {
    _grayEnv: GrayEnv = GrayEnv.PROD;
    _region: string = "region1";

    /**
     * 可以通过abc-conf.ini配置程序的代理列表，程序启动时将会随机选择一个使用，并将选择结果保存到networkConfig
     * [proxy]
     #proxyServers='socks5://10.200.0.2:8000;socks5://10.200.0.3:8000'
     #proxyBypassList='<local>'
     #proxyCarrier=hangzhou_wasu
     */
    networkConfig?: {
        proxyServer: string;
        proxyBypassList: string
    }


    /**
     * 是否是从abc-service 服务启动的
     */
    isLaunchFromAbcService = false; //launch-from-abc-service
    private _lastLoginClinicId?: string; //最后一次登录的clinicId,并不代表当前登录的clinicId


    /**
     * 是否是开发环境
     */
    get isDev(): boolean {
        return abcConfig.env === 'dev';
    }

    /**
     * 是否是测试环境
     */
    get isTest(): boolean {
        return abcConfig.env === 'test';
    }

    /**
     * 是否是正式环境
     */

    get isRelease(): boolean {
        return abcConfig.env === 'prod';
    }

    grayEnv(): GrayEnv {
        return this._grayEnv;
    }


    region(): string {
        return this._region;
    }

    uuid(): string {
        let uuid = sharedPreferences.getObject(kUUID);
        if (!uuid) {
            uuid = require('uuid').v4();
            sharedPreferences.setObject(kUUID, uuid);
        }

        return uuid;
    }

    init() {
        const region = sharedPreferences.getObject(kLastRegion);
        if (region && typeof region === 'string' && region != 'undefined' && region != 'null') {
            this._region = region;
        }

        const grayEnvObj = sharedPreferences.getObject(kLastGrayEnv);
        if (!_.isNil(grayEnvObj) && grayEnvObj in GrayEnv) {
            this._grayEnv = grayEnvObj;
        }

        this._lastLoginClinicId = sharedPreferences.getObject(kLastLoginClinicId);
    }

    public setRegion(region: string, grayEnv: GrayEnv = GrayEnv.PROD) {
        this._region = region;
        if (this._grayEnv != grayEnv) {
            this._grayEnv = grayEnv;
        }

        sharedPreferences.setObject(kLastRegion, region);
        sharedPreferences.setObject(kLastGrayEnv, grayEnv);
    }

    constructor() {
        this.isLaunchFromAbcService = getCLIOption("launch-from-abc-service", "false") === "true";
    }

    setLoginInfo(chainId: string, clinicId: string, employeeId: string) {
        this._lastLoginClinicId = clinicId;
        sharedPreferences.setObject(kLastLoginClinicId, clinicId);
        this._lastLoginClinicId = clinicId;
    }
}


const abcEnvironment = new AbcEnvironment();

export {abcEnvironment};
