/**
 *  命令行参数
 */

function getCLIOption(key: string, defaultValue: any) {
    let finalValue = defaultValue;
    forEachArg(process.argv, (key1, value) => {
        if (key === key1) {
            finalValue = value;
            return false;
        }
    });

    return finalValue;
}

function hasCLIParams(key: string) {
    let argIndex = process.argv.indexOf(key);
    return argIndex > 0;
}

let entryUrl = getCLIOption('entryurl', '');
let openDevTools = hasCLIParams('opendevtools');


function forEachArg(argv: string[], callback: (key: string, value?: string) => boolean | void) {
    const PREFIX = "--";
    for (let arg of (argv ?? process.argv)) {
        if (!arg.startsWith(PREFIX)) continue;
        arg = arg.substr(PREFIX.length);
        const tokens = arg.split("=");
        let key: string;
        let value: string = null;
        if (tokens.length >= 2) {
            key = tokens[0];
            value = tokens.slice(1).join('=');
        } else {
            key = arg;
        }

        if (callback(key, value) === false) return;
    }

}

/**
 * 从程序启动参数提取key-value，以--开头的参数才会提取对应的key-value
 * @returns {Map<string, string>}
 */
function getCLIOptions(argv?: string[]): Map<string, any> {
    const map = new Map<string, any>();
    forEachArg((argv ?? process.argv), (key, value) => {
        map.set(key, value);
    });

    return map;
}


export {
    entryUrl,
    openDevTools,
    getCLIOption,
    getCLIOptions,
    hasCLIParams
}
