{"rules": [{"remote": "static-common-cdn.abcyun.cn/", "name": "abc-static-common", "global": true}, {"remote": "cis-static-common.oss-cn-shanghai.aliyuncs.com/", "name": "abc-static-common"}, {"remote": "static-pre-cdn.abcyun.cn/pc/static/img", "name": "pc-img", "filePrefix": "/static/img"}, {"remote": "static-pre-cdn.abcyun.cn/pc", "name": "pc"}, {"remote": "static-pre-cdn.abcyun.cn/abc-fe-engine/", "name": "abc-fe-engine"}, {"remote": "static-pre-cdn.abcyun.cn/abc-emr-editor-sdk/", "name": "abc-emr-editor-sdk"}, {"remote": "static-pre-cdn.abcyun.cn/abc-public-health/", "name": "abc-public-health"}, {"remote": "static-pre-cdn.abcyun.cn/abc-medical-imaging-viewer-sdk/", "name": "abc-medical-imaging-viewer-sdk"}, {"remote": "static-pre-cdn.abcyun.cn/abc-lis/", "name": "abc-lis"}, {"remote": "static-pre-cdn.abcyun.cn/abc-print/", "name": "abc-print"}, {"remote": "static-pre-cdn.abcyun.cn/abc-micro-frontend/social/static/img", "name": "abc-social-img", "filePrefix": "/static/img"}, {"remote": "static-pre-cdn.abcyun.cn/abc-micro-frontend/social/", "name": "abc-social"}, {"remote": "static-pre-cdn.abcyun.cn/abc-micro-frontend/mall/", "name": "abc-b2b-mall"}, {"remote": "cd-cis-static-pre.oss-cn-chengdu.aliyuncs.com/abc-fed-config/", "name": "abc-fed-config"}, {"remote": "static-pre-cdn.abcyun.cn/mf-order-cloud/", "name": "mf-order-cloud"}, {"remote": "static-pre-cdn.abcyun.cn/order-cloud-desktop-assistant", "name": "order-cloud-desktop-assistant"}, {"remote": "static-gray-cdn.abcyun.cn/pc/static/img", "name": "pc-img", "filePrefix": "/static/img"}, {"remote": "static-gray-cdn.abcyun.cn/pc", "name": "pc"}, {"remote": "static-gray-cdn.abcyun.cn/abc-fe-engine/", "name": "abc-fe-engine"}, {"remote": "static-gray-cdn.abcyun.cn/abc-emr-editor-sdk/", "name": "abc-emr-editor-sdk"}, {"remote": "static-gray-cdn.abcyun.cn/abc-public-health/", "name": "abc-public-health"}, {"remote": "static-gray-cdn.abcyun.cn/abc-medical-imaging-viewer-sdk/", "name": "abc-medical-imaging-viewer-sdk"}, {"remote": "static-gray-cdn.abcyun.cn/abc-lis/", "name": "abc-lis"}, {"remote": "static-gray-cdn.abcyun.cn/abc-print/", "name": "abc-print"}, {"remote": "static-gray-cdn.abcyun.cn/abc-micro-frontend/social/static/img", "name": "abc-social-img", "filePrefix": "/static/img"}, {"remote": "static-gray-cdn.abcyun.cn/abc-micro-frontend/social/", "name": "abc-social"}, {"remote": "static-gray-cdn.abcyun.cn/abc-micro-frontend/mall/", "name": "abc-b2b-mall"}, {"remote": "cis-static-gray.oss-cn-shanghai.aliyuncs.com/abc-fed-config/", "name": "abc-fed-config"}, {"remote": "static-gray-cdn.abcyun.cn/mf-order-cloud/", "name": "mf-order-cloud"}, {"remote": "static-gray-cdn.abcyun.cn/order-cloud-desktop-assistant", "name": "order-cloud-desktop-assistant"}, {"remote": "cis-static-prod.oss-cn-shanghai.aliyuncs.com/pc/static/img", "name": "pc-img", "filePrefix": "/static/img"}, {"remote": "cis-static-prod.oss-cn-shanghai.aliyuncs.com/pc", "name": "pc"}, {"remote": "cis-static-prod.oss-cn-shanghai.aliyuncs.com/abc-fe-engine/", "name": "abc-fe-engine"}, {"remote": "cis-static-prod.oss-cn-shanghai.aliyuncs.com/abc-emr-editor-sdk/", "name": "abc-emr-editor-sdk"}, {"remote": "cis-static-prod.oss-cn-shanghai.aliyuncs.com/abc-public-health/", "name": "abc-public-health"}, {"remote": "cis-static-prod.oss-cn-shanghai.aliyuncs.com/abc-medical-imaging-viewer-sdk/", "name": "abc-medical-imaging-viewer-sdk"}, {"remote": "cis-static-prod.oss-cn-shanghai.aliyuncs.com/abc-lis/", "name": "abc-lis"}, {"remote": "cis-static-prod.oss-cn-shanghai.aliyuncs.com/abc-print/", "name": "abc-print"}, {"remote": "cis-static-prod.oss-cn-shanghai.aliyuncs.com/abc-micro-frontend/social/static/img", "name": "abc-social-img", "filePrefix": "/static/img"}, {"remote": "cis-static-prod.oss-cn-shanghai.aliyuncs.com/abc-micro-frontend/social/", "name": "abc-social"}, {"remote": "cis-static-prod.oss-cn-shanghai.aliyuncs.com/abc-micro-frontend/mall/", "name": "abc-b2b-mall"}, {"remote": "cis-static-prod.oss-cn-shanghai.aliyuncs.com/abc-fed-config/", "name": "abc-fed-config"}, {"remote": "cis-static-prod.oss-cn-shanghai.aliyuncs.com/mf-order-cloud/", "name": "mf-order-cloud"}, {"remote": "cis-static-prod.oss-cn-shanghai.aliyuncs.com/order-cloud-desktop-assistant", "name": "order-cloud-desktop-assistant"}, {"remote": "cis-static-prod.oss-cn-shanghai.aliyuncs.com/abc-login", "name": "abc-static-login"}, {"remote": "global.abcyun.cn", "name": "abc-static-login", "rule": ["rewrite /* /login.html"], "id": 3, "global": true}, {"remote": "region2.abcyun.cn/order-cloud-desktop-assistant", "name": "order-cloud-desktop-assistant", "rule": ["rewrite /* /index.html"]}, {"remote": "region1.abcyun.cn", "type": "rewrite", "target": 2}, {"remote": "region2.abcyun.cn", "type": "rewrite", "target": 2}, {"remote": "www.abcyun.cn/order-cloud-desktop-assistant", "name": "order-cloud-desktop-assistant", "rule": ["rewrite /* /index.html"]}, {"remote": "www.abcyun.cn", "name": "pc", "preInterceptor": [{"path": "^/auth-callback/*", "script": "module.exports=function(options){const{target,originalUrl,pluginInfo}=options;console.log(`[preIntercept], target: ${target}, originalUrl: ${originalUrl}, pluginInfo: ${pluginInfo}`);if(pluginInfo&&pluginInfo.onlineVersion){const versionMatch=pluginInfo.onlineVersion.match(/^\\d+-pc-[pgv](\\d{4})\\.(\\d{2})\\.\\d+/);if(versionMatch){const year=parseInt(versionMatch[1],10);const week=parseInt(versionMatch[2],10);if(year>2024||(year===2024&&week>44)){const url=new URL(originalUrl);const queryTarget=url.searchParams.get('target');let newPath;if(queryTarget==='pharmacy'){newPath='/biz-pharmacy/region-auth';}else if(queryTarget==='hospital'){newPath='/hospital/region-auth';}else if(queryTarget==='chain'){newPath='/chain/region-auth';}else if(queryTarget==='clinic'){newPath='/region-auth';}if(newPath){console.log(`[preIntercept], newPath: ${newPath}`);const newTarget=newPath+url.search;return{preventDefault:false,target:newTarget};}}}else{console.log(`[preIntercept], pluginInfo.onlineVersion: ${pluginInfo.onlineVersion}, skipping as version is before 2024.45`);}}return{preventDefault:false,target};}"}], "rule": ["redirect ^/login abcyun://global.abcyun.cn/login?from=${originalOrigin}", "redirect ^/entry abcyun://global.abcyun.cn/entry?from=${originalOrigin}", "rewrite ^/$ /static/app.html", "rewrite ^/external/* /external-app.html", "rewrite ^/static/migrate-localstorage.html$ /static/migrate-localstorage.html", "rewrite ^/hospital/* /hospital-app.html", "rewrite ^/biz-pharmacy/* /pharmacy-app.html", "rewrite ^/air-pharmacy-introduce$ /home.html", "rewrite ^/auth-callback/* /home.html", "rewrite ^/medical-development/* /home.html", "rewrite ^/examination-equipment-sale-activity/* /home.html", "rewrite ^/medical-device-promotion/* /home.html", "rewrite ^/record-guidelines/* /home.html", "rewrite ^/chain/* /chain.html", "rewrite ^/static/(.*) /static/$1", "rewrite /* /index.html"], "id": 2}, {"path": "^/api", "type": "api"}, {"remote": "g.alicdn.com", "targetProtocol": "https", "type": "bypass"}, {"remote": "cf.aliyun.com", "targetProtocol": "https", "type": "bypass"}, {"remote": "s.union.360.cn", "targetProtocol": "https", "type": "bypass"}, {"remote": "static-pre-cdn.abcyun.cn/abc-desktop-app-ext-sdk", "targetProtocol": "https", "type": "bypass"}, {"remote": "static-gray-cdn.abcyun.cn/abc-desktop-app-ext-sdk", "targetProtocol": "https", "type": "bypass"}, {"remote": "static-prod-cdn.abcyun.cn/abc-desktop-app-ext-sdk", "targetProtocol": "https", "type": "bypass"}, {"remote": "cis-static-prod.oss-cn-shanghai.aliyuncs.com/abc-desktop-app-ext-sdk", "targetProtocol": "https", "type": "bypass"}]}