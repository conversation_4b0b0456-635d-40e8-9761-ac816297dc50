{"rules": [{"remote": "static-common-cdn.abcyun.cn/", "name": "abc-static-common", "global": true}, {"remote": "cis-static-common.oss-cn-shanghai.aliyuncs.com/", "name": "abc-static-common"}, {"remote": "static-test-cdn.abczs.cn/abc-fe-engine/", "name": "abc-fe-engine"}, {"remote": "static-test-cdn.abczs.cn/abc-emr-editor-sdk/", "name": "abc-emr-editor-sdk"}, {"remote": "static-test-cdn.abczs.cn/abc-public-health/", "name": "abc-public-health"}, {"remote": "static-test-cdn.abczs.cn/abc-medical-imaging-viewer-sdk/", "name": "abc-medical-imaging-viewer-sdk"}, {"remote": "static-test-cdn.abczs.cn/abc-lis/", "name": "abc-lis"}, {"remote": "static-test-cdn.abczs.cn/abc-print/", "name": "abc-print"}, {"remote": "static-test-cdn.abczs.cn/abc-micro-frontend/social/static/img", "name": "abc-social-img", "filePrefix": "/static/img"}, {"remote": "static-test-cdn.abczs.cn/abc-micro-frontend/social/", "name": "abc-social"}, {"remote": "static-test-cdn.abczs.cn/abc-micro-frontend/mall/", "name": "abc-b2b-mall"}, {"remote": "static-test-cdn.abczs.cn/abc-login", "name": "abc-static-login"}, {"remote": "cis-static-test.oss-cn-shanghai.aliyuncs.com/abc-fed-config/", "name": "abc-fed-config"}, {"remote": "static-test-cdn.abczs.cn/mf-order-cloud/", "name": "mf-order-cloud"}, {"remote": "static-test-cdn.abczs.cn/order-cloud-desktop-assistant", "name": "order-cloud-desktop-assistant"}, {"remote": "global-test.abczs.cn", "name": "abc-static-login", "rule": ["rewrite /* /login.html"], "id": 3, "global": true}, {"remote": "static-test-cdn.abczs.cn/pc/static/img", "name": "pc-img", "filePrefix": "/static/img"}, {"remote": "static-test-cdn.abczs.cn/pc", "name": "pc"}, {"remote": "region1-test.abczs.cn", "type": "rewrite", "target": 2}, {"remote": "region2-test.abczs.cn", "type": "rewrite", "target": 2}, {"remote": "test.abczs.cn/order-cloud-desktop-assistant", "name": "order-cloud-desktop-assistant", "rule": ["rewrite /* /index.html"]}, {"remote": "test.abczs.cn", "name": "pc", "preInterceptor": [{"path": "^/auth-callback/*", "script": "module.exports=function(options){const{target,originalUrl,pluginInfo}=options;console.log(`[preIntercept], target: ${target}, originalUrl: ${originalUrl}, pluginInfo: ${pluginInfo}`);if(pluginInfo&&(!pluginInfo.onlineVersion.includes('pc-t2024.41.04')&&!pluginInfo.onlineVersion.includes('pc-t2024.42.04'))){console.log(`[preIntercept], pluginInfo.onlineVersion: ${pluginInfo.onlineVersion}, expect version: pc-t2024.41.04 skip`);return{preventDefault:false,target};}const url=new URL(originalUrl);const queryTarget=url.searchParams.get('target');let newPath;if(queryTarget==='pharmacy'){newPath='/biz-pharmacy/region-auth';}if(queryTarget==='hospital'){newPath='/hospital/region-auth';}if(queryTarget==='chain'){newPath='/chain/region-auth';}if(queryTarget==='clinic'){newPath='/region-auth';}if(newPath){const newTarget=newPath+url.search;return{preventDefault:false,target:newTarget};}return{preventDefault:false,target};};"}], "rule": ["redirect ^/login abcyun://global-test.abczs.cn/login?from=${originalOrigin}", "redirect ^/entry abcyun://global-test.abczs.cn/entry?from=${originalOrigin}", "rewrite ^/$ /static/app.html", "rewrite ^/external/* /external-app.html", "rewrite ^/static/migrate-localstorage.html$ /static/migrate-localstorage.html", "rewrite ^/hospital/* /hospital-app.html", "rewrite ^/biz-pharmacy/* /pharmacy-app.html", "rewrite ^/air-pharmacy-introduce$ /home.html", "rewrite ^/auth-callback/* /home.html", "rewrite ^/medical-development/* /home.html", "rewrite ^/examination-equipment-sale-activity/* /home.html", "rewrite ^/medical-device-promotion/* /home.html", "rewrite ^/record-guidelines/* /home.html", "rewrite ^/chain/* /chain.html", "rewrite ^/static/(.*) /static/$1", "rewrite /* /index.html"], "id": 2}, {"path": "^/api", "type": "api"}, {"remote": "g.alicdn.com", "targetProtocol": "https", "type": "bypass"}, {"remote": "cf.aliyun.com", "targetProtocol": "https", "type": "bypass"}, {"remote": "s.union.360.cn", "targetProtocol": "https", "type": "bypass"}, {"remote": "static-test-cdn.abczs.cn/abc-desktop-app-ext-sdk", "targetProtocol": "https", "type": "bypass"}]}