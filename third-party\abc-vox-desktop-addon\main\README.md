# Window Manager - 窗口管理器

一个简化版的 Electron 窗口管理器，提供窗口的创建、管理和基本操作功能。

## 📁 项目结构

```
main/
├── managers/                    # 管理器模块
│   ├── index.ts                # 管理器统一入口
│   └── window-manager/         # 窗口管理器
│       ├── manager.ts          # 窗口管理器主类
│       └── instance.ts         # 窗口实例类
├── types/                      # 类型定义
│   └── window-types.ts        # 窗口相关类型
├── index.ts                    # 主入口文件 (TypeScript)
└── README.md                   # 说明文档
```

## 🛠 快速开始

### 1. 主进程初始化

```javascript
const { init } = require('./web/main');

// 在 Electron 主进程中初始化
init(electron);

// 现在可以通过 electron.windowManager 访问窗口管理 API
const windowAPI = electron.windowManager;
```

## 📋 API 参考

### 窗口管理方法

所有方法都挂载在 `electron.windowManager` 上：

```javascript
// 创建窗口
await electron.windowManager.createWindow(options)

// 销毁窗口
await electron.windowManager.destroyWindow(windowId)

// 显示/隐藏窗口
await electron.windowManager.showWindow(windowId)
await electron.windowManager.hideWindow(windowId)

// 调整窗口大小和位置
await electron.windowManager.resizeWindow(windowId, { width: 1000, height: 700 })
await electron.windowManager.moveWindow(windowId, { x: 100, y: 100 })

// 更新窗口属性
await electron.windowManager.updateWindow(windowId, {
    title: '新标题',
    backgroundColor: '#f0f0f0'
})

// 查询方法
const window = electron.windowManager.getWindow(windowId)
const allWindows = electron.windowManager.getAllWindows()
const windowCount = electron.windowManager.getWindowCount()
const hasWindow = electron.windowManager.hasWindow(windowId)

// 工具方法
const windowId = electron.windowManager.generateWindowId('prefix')
await electron.windowManager.closeAllWindows(excludeWindowIds)
```

## 🔧 窗口选项

### 窗口创建选项

```javascript
// 创建窗口时可以传入的选项
{
    id: 'window-id',             // 窗口唯一标识 (必需)
    url: 'https://example.com',  // 加载的URL (必需)
    width: 800,                  // 窗口宽度 (必需)
    height: 600,                 // 窗口高度 (必需)
    x: 100,                      // 窗口X位置 (可选)
    y: 100,                      // 窗口Y位置 (可选)
    title: '窗口标题',           // 窗口标题 (可选)
    resizable: true,             // 是否可调整大小 (可选，默认true)
    minimizable: true,           // 是否可最小化 (可选，默认true)
    maximizable: true,           // 是否可最大化 (可选，默认true)
    closable: true,              // 是否可关闭 (可选，默认true)
    alwaysOnTop: false,          // 是否置顶 (可选，默认false)
    modal: false,                // 是否模态 (可选，默认false)
    parent: 'parent-window-id',  // 父窗口ID (可选)
    backgroundColor: '#ffffff',  // 背景色 (可选，默认#ffffff)
    frame: true,                 // 是否显示边框 (可选，默认true)
    transparent: false,          // 是否透明 (可选，默认false)
    show: true,                  // 是否立即显示 (可选，默认true)
    center: false,               // 是否居中 (可选，默认false)
    webPreferences: {            // Web偏好设置 (可选)
        nodeIntegration: true,
        contextIsolation: false
    },
    lifecycle: {                 // 生命周期钩子 (可选)
        // 见下面的生命周期钩子说明
    }
}
```

### 生命周期钩子

```javascript
lifecycle: {
    onBeforeCreate: async (options) => {
        // 窗口创建前执行
        console.log('即将创建窗口');
    },
    onAfterCreate: async (instance) => {
        // 窗口创建后执行
        console.log('窗口创建完成');
    },
    onBeforeShow: async (instance) => {
        // 窗口显示前执行
        console.log('即将显示窗口');
    },
    onAfterShow: async (instance) => {
        // 窗口显示后执行
        console.log('窗口已显示');
    },
    onBeforeHide: async (instance) => {
        // 窗口隐藏前执行
        console.log('即将隐藏窗口');
    },
    onAfterHide: async (instance) => {
        // 窗口隐藏后执行
        console.log('窗口已隐藏');
    },
    onBeforeClose: async (instance) => {
        // 窗口关闭前执行，返回 false 可以阻止关闭
        const shouldClose = await confirmClose();
        return shouldClose;
    },
    onAfterClose: async (instance) => {
        // 窗口关闭后执行
        console.log('窗口已关闭');
    },
    onFocus: async (instance) => {
        // 窗口获得焦点时执行
        console.log('窗口获得焦点');
    },
    onBlur: async (instance) => {
        // 窗口失去焦点时执行
        console.log('窗口失去焦点');
    }
}
```
