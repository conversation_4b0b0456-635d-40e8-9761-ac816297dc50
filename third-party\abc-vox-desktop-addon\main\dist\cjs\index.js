"use strict";
var __webpack_require__ = {};
(()=>{
    __webpack_require__.d = (exports1, definition)=>{
        for(var key in definition)if (__webpack_require__.o(definition, key) && !__webpack_require__.o(exports1, key)) Object.defineProperty(exports1, key, {
            enumerable: true,
            get: definition[key]
        });
    };
})();
(()=>{
    __webpack_require__.o = (obj, prop)=>Object.prototype.hasOwnProperty.call(obj, prop);
})();
(()=>{
    __webpack_require__.r = (exports1)=>{
        if ('undefined' != typeof Symbol && Symbol.toStringTag) Object.defineProperty(exports1, Symbol.toStringTag, {
            value: 'Module'
        });
        Object.defineProperty(exports1, '__esModule', {
            value: true
        });
    };
})();
var __webpack_exports__ = {};
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
    init: ()=>index_init,
    destroy: ()=>index_destroy
});
class Instance {
    id;
    browserWindow;
    options;
    _isDestroyed = false;
    _eventListeners = new Map();
    constructor(browserWindow, options){
        this.id = options.id;
        this.browserWindow = browserWindow;
        this.options = {
            ...options
        };
        this._bindEventListeners();
    }
    _bindEventListeners() {
        const onFocus = async ()=>{
            if (this.options.lifecycle?.onFocus) await this.options.lifecycle.onFocus(this);
        };
        this.browserWindow.on('focus', onFocus);
        this._eventListeners.set('focus', onFocus);
        const onBlur = async ()=>{
            if (this.options.lifecycle?.onBlur) await this.options.lifecycle.onBlur(this);
        };
        this.browserWindow.on('blur', onBlur);
        this._eventListeners.set('blur', onBlur);
        const onClose = async (event)=>{
            if (this.options.lifecycle?.onBeforeClose) {
                const shouldClose = await this.options.lifecycle.onBeforeClose(this);
                if (false === shouldClose) return void event.preventDefault();
            }
        };
        this.browserWindow.on('close', onClose);
        this._eventListeners.set('close', onClose);
        const onClosed = async ()=>{
            this._isDestroyed = true;
            if (this.options.lifecycle?.onAfterClose) await this.options.lifecycle.onAfterClose(this);
            this._cleanup();
        };
        this.browserWindow.on('closed', onClosed);
        this._eventListeners.set('closed', onClosed);
    }
    _cleanup() {
        for (const [eventName, listener] of this._eventListeners)if (this.browserWindow && !this.browserWindow.isDestroyed()) this.browserWindow.removeListener(eventName, listener);
        this._eventListeners.clear();
        if (this.options.lifecycle) Object.keys(this.options.lifecycle).forEach((key)=>{
            delete this.options.lifecycle[key];
        });
    }
    async show() {
        if (this._isDestroyed) return;
        if (this.options.lifecycle?.onBeforeShow) await this.options.lifecycle.onBeforeShow(this);
        this.browserWindow.show();
        if (this.options.lifecycle?.onAfterShow) await this.options.lifecycle.onAfterShow(this);
    }
    async hide() {
        if (this._isDestroyed) return;
        if (this.options.lifecycle?.onBeforeHide) await this.options.lifecycle.onBeforeHide(this);
        this.browserWindow.hide();
        if (this.options.lifecycle?.onAfterHide) await this.options.lifecycle.onAfterHide(this);
    }
    async close() {
        if (this._isDestroyed) return;
        this._cleanup();
        this.browserWindow.close();
    }
    destroy() {
        if (this._isDestroyed) return;
        this._cleanup();
        this._isDestroyed = true;
        if (this.browserWindow && !this.browserWindow.isDestroyed()) this.browserWindow.destroy();
    }
    async resize(size) {
        if (this._isDestroyed) return;
        this.browserWindow.setSize(size.width, size.height);
    }
    async move(position) {
        if (this._isDestroyed) return;
        this.browserWindow.setPosition(position.x, position.y);
    }
    setBounds(bounds) {
        if (this._isDestroyed) return;
        const currentBounds = this.browserWindow.getBounds();
        const newBounds = {
            ...currentBounds,
            ...bounds
        };
        this.browserWindow.setBounds(newBounds);
    }
    getBounds() {
        if (this._isDestroyed) return {
            x: 0,
            y: 0,
            width: 0,
            height: 0
        };
        return this.browserWindow.getBounds();
    }
    setBackgroundColor(color) {
        if (this._isDestroyed) return;
        this.browserWindow.setBackgroundColor(color);
    }
    setTitle(title) {
        if (this._isDestroyed) return;
        this.browserWindow.setTitle(title);
    }
    setAlwaysOnTop(flag) {
        if (this._isDestroyed) return;
        this.browserWindow.setAlwaysOnTop(flag);
    }
    setOpacity(opacity) {
        if (this._isDestroyed) return;
        this.browserWindow.setOpacity(opacity);
    }
    focus() {
        if (this._isDestroyed) return;
        this.browserWindow.focus();
    }
    minimize() {
        if (this._isDestroyed) return;
        this.browserWindow.minimize();
    }
    maximize() {
        if (this._isDestroyed) return;
        this.browserWindow.maximize();
    }
    restore() {
        if (this._isDestroyed) return;
        this.browserWindow.restore();
    }
    isVisible() {
        if (this._isDestroyed) return false;
        return this.browserWindow.isVisible();
    }
    isMinimized() {
        if (this._isDestroyed) return false;
        return this.browserWindow.isMinimized();
    }
    isMaximized() {
        if (this._isDestroyed) return false;
        return this.browserWindow.isMaximized();
    }
    isFullScreen() {
        if (this._isDestroyed) return false;
        return this.browserWindow.isFullScreen();
    }
    isFocused() {
        if (this._isDestroyed) return false;
        return this.browserWindow.isFocused();
    }
    isDestroyed() {
        return this._isDestroyed;
    }
}
class Manager {
    _windows = new Map();
    _electron;
    _initialized = false;
    init(electron) {
        if (this._initialized) return void console.warn('WindowManager already initialized');
        this._electron = electron;
        electron.windowManager = {
            createWindow: this.createWindow.bind(this),
            destroyWindow: this.destroyWindow.bind(this),
            showWindow: this.showWindow.bind(this),
            hideWindow: this.hideWindow.bind(this),
            resizeWindow: this.resizeWindow.bind(this),
            moveWindow: this.moveWindow.bind(this),
            updateWindow: this.updateWindow.bind(this),
            getWindow: this.getWindow.bind(this),
            getAllWindows: this.getAllWindows.bind(this),
            hasWindow: this.hasWindow.bind(this),
            getWindowCount: this.getWindowCount.bind(this),
            generateWindowId: this.generateWindowId.bind(this),
            closeAllWindows: this.closeAllWindows.bind(this)
        };
        this._initialized = true;
        console.log('WindowManager initialized and API mounted on electron.windowManager');
    }
    async createWindow(options) {
        if (!this._initialized) return {
            success: false,
            error: 'WindowManager not initialized'
        };
        try {
            if (options.lifecycle?.onBeforeCreate) await options.lifecycle.onBeforeCreate(options);
            const browserWindow = new this._electron.BrowserWindow(this._buildBrowserWindowOptions(options));
            const instance = new Instance(browserWindow, options);
            if (options.lifecycle?.onAfterCreate) await options.lifecycle.onAfterCreate(instance);
            this._registerWindow(instance);
            if (options.url) await browserWindow.loadURL(options.url);
            return {
                success: true,
                id: options.id
            };
        } catch (error) {
            console.error('Failed to create window:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    async destroyWindow(windowId) {
        try {
            const instance = this._windows.get(windowId);
            if (!instance) return {
                success: false,
                error: 'Window not found'
            };
            this._unregisterWindow(windowId);
            if ('function' == typeof instance.destroy) instance.destroy();
            else await instance.close();
            return {
                success: true
            };
        } catch (error) {
            console.error('Failed to destroy window:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    async showWindow(windowId) {
        try {
            const instance = this._windows.get(windowId);
            if (!instance) return {
                success: false,
                error: 'Window not found'
            };
            await instance.show();
            return {
                success: true
            };
        } catch (error) {
            console.error('Failed to show window:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    async hideWindow(windowId) {
        try {
            const instance = this._windows.get(windowId);
            if (!instance) return {
                success: false,
                error: 'Window not found'
            };
            await instance.hide();
            return {
                success: true
            };
        } catch (error) {
            console.error('Failed to hide window:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    async resizeWindow(windowId, size) {
        try {
            const instance = this._windows.get(windowId);
            if (!instance) return {
                success: false,
                error: 'Window not found'
            };
            await instance.resize(size);
            return {
                success: true
            };
        } catch (error) {
            console.error('Failed to resize window:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    async moveWindow(windowId, position) {
        try {
            const instance = this._windows.get(windowId);
            if (!instance) return {
                success: false,
                error: 'Window not found'
            };
            await instance.move(position);
            return {
                success: true
            };
        } catch (error) {
            console.error('Failed to move window:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    async updateWindow(windowId, updates) {
        try {
            const instance = this._windows.get(windowId);
            if (!instance) return {
                success: false,
                error: 'Window not found'
            };
            if (updates.url && updates.url !== instance.options.url) await instance.browserWindow.loadURL(updates.url);
            if (updates.title) instance.setTitle(updates.title);
            if (updates.backgroundColor) instance.setBackgroundColor(updates.backgroundColor);
            if (void 0 !== updates.alwaysOnTop) instance.setAlwaysOnTop(updates.alwaysOnTop);
            if (updates.width || updates.height) {
                const size = {
                    width: updates.width || instance.options.width,
                    height: updates.height || instance.options.height
                };
                await instance.resize(size);
            }
            if (void 0 !== updates.x || void 0 !== updates.y) {
                const position = {
                    x: void 0 !== updates.x ? updates.x : instance.getBounds().x,
                    y: void 0 !== updates.y ? updates.y : instance.getBounds().y
                };
                await instance.move(position);
            }
            Object.assign(instance.options, updates);
            return {
                success: true
            };
        } catch (error) {
            console.error('Failed to update window:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    getWindow(windowId) {
        const instance = this._windows.get(windowId);
        if (!instance) return null;
        return {
            id: instance.id,
            options: instance.options,
            isVisible: instance.isVisible(),
            isDestroyed: instance.isDestroyed(),
            bounds: instance.getBounds()
        };
    }
    getAllWindows() {
        return Array.from(this._windows.values()).map((instance)=>({
                id: instance.id,
                options: instance.options,
                isVisible: instance.isVisible(),
                isDestroyed: instance.isDestroyed(),
                bounds: instance.getBounds()
            }));
    }
    hasWindow(windowId) {
        return this._windows.has(windowId);
    }
    getWindowCount() {
        return this._windows.size;
    }
    generateWindowId(prefix = 'window') {
        let id;
        let counter = 1;
        do {
            id = `${prefix}_${Date.now()}_${counter}`;
            counter++;
        }while (this._windows.has(id));
        return id;
    }
    async closeAllWindows(excludeWindowIds) {
        try {
            const windowsToClose = Array.from(this._windows.values()).filter((window)=>!excludeWindowIds?.includes(window.id));
            let closedCount = 0;
            const closePromises = windowsToClose.map(async (window)=>{
                try {
                    await window.close();
                    closedCount++;
                } catch (error) {
                    console.error(`Failed to close window ${window.id}:`, error);
                }
            });
            await Promise.all(closePromises);
            return {
                success: true,
                count: closedCount
            };
        } catch (error) {
            console.error('Failed to close all windows:', error);
            return {
                success: false,
                count: 0,
                error: error.message
            };
        }
    }
    _buildBrowserWindowOptions(options) {
        return {
            width: options.width,
            height: options.height,
            x: options.x,
            y: options.y,
            minWidth: options.minWidth,
            minHeight: options.minHeight,
            maxWidth: options.maxWidth,
            maxHeight: options.maxHeight,
            resizable: false !== options.resizable,
            minimizable: false !== options.minimizable,
            maximizable: false !== options.maximizable,
            closable: false !== options.closable,
            alwaysOnTop: options.alwaysOnTop || false,
            modal: options.modal || false,
            parent: options.parent ? this._windows.get(options.parent)?.browserWindow : void 0,
            backgroundColor: options.backgroundColor || '#ffffff',
            frame: false !== options.frame,
            transparent: options.transparent || false,
            show: false !== options.show,
            center: options.center || false,
            title: options.title || '',
            icon: options.icon,
            webPreferences: options.webPreferences || {
                nodeIntegration: true,
                contextIsolation: false
            }
        };
    }
    _registerWindow(instance) {
        this._windows.set(instance.id, instance);
        const windowId = instance.id;
        const onClosed = ()=>{
            this._unregisterWindow(windowId);
        };
        instance.browserWindow.on('closed', onClosed);
        instance._managerCleanup = ()=>{
            if (instance.browserWindow && !instance.browserWindow.isDestroyed()) instance.browserWindow.removeListener('closed', onClosed);
        };
    }
    _unregisterWindow(windowId) {
        const instance = this._windows.get(windowId);
        if (instance) {
            if (instance._managerCleanup) {
                instance._managerCleanup();
                delete instance._managerCleanup;
            }
            this._windows.delete(windowId);
        }
    }
    destroy() {
        try {
            for (const [windowId, instance] of this._windows)try {
                if (instance._managerCleanup) {
                    instance._managerCleanup();
                    delete instance._managerCleanup;
                }
                if ('function' == typeof instance.destroy) instance.destroy();
                else if (!instance.isDestroyed()) instance.browserWindow.destroy();
            } catch (error) {
                console.error(`Error destroying window ${windowId}:`, error);
            }
            this._windows.clear();
            this._initialized = false;
            if (this._electron && this._electron.windowManager) delete this._electron.windowManager;
            this._electron = null;
            console.log('WindowManager destroyed');
        } catch (error) {
            console.error('Error during WindowManager destruction:', error);
        }
    }
}
let windowManager = null;
function init(electron) {
    if (!electron) throw new Error('Electron API is required');
    try {
        windowManager = new Manager();
        windowManager.init(electron);
        console.log('All managers initialized successfully');
    } catch (error) {
        console.error('Failed to initialize managers:', error);
        throw error;
    }
}
function destroy() {
    try {
        if (windowManager) {
            windowManager.destroy();
            windowManager = null;
        }
        console.log('All managers destroyed');
    } catch (error) {
        console.error('Failed to destroy managers:', error);
    }
}
function index_init(electron) {
    if (!electron) throw new Error('Electron API is required');
    try {
        init(electron);
        console.log('Main module initialized successfully');
    } catch (error) {
        console.error('Failed to initialize main module:', error);
        throw error;
    }
}
function index_destroy() {
    try {
        destroy();
        console.log('Main module destroyed');
    } catch (error) {
        console.error('Failed to destroy main module:', error);
    }
}
exports.destroy = __webpack_exports__.destroy;
exports.init = __webpack_exports__.init;
for(var __webpack_i__ in __webpack_exports__)if (-1 === [
    "destroy",
    "init"
].indexOf(__webpack_i__)) exports[__webpack_i__] = __webpack_exports__[__webpack_i__];
Object.defineProperty(exports, '__esModule', {
    value: true
});
