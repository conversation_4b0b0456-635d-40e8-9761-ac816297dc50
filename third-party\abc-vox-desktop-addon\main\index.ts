/**
 * Main Module - 主模块入口
 * 串联 Web 和 Electron，提供统一的功能管理接口
 *
 * 使用方式：
 * 1. 在 Electron 主进程中调用 init 方法初始化
 * 2. 各个 manager 会直接在 electron 对象上挂载 API
 */

import * as managers from './managers';

/**
 * 初始化主模块
 * @param electron - Electron API 对象
 */
export function init(electron: any): void {
    if (!electron) {
        throw new Error('Electron API is required');
    }

    try {
        // 初始化所有管理器
        managers.init(electron);

        console.log('Main module initialized successfully');
    } catch (error) {
        console.error('Failed to initialize main module:', error);
        throw error;
    }
}

/**
 * 销毁主模块
 */
export function destroy(): void {
    try {
        // 销毁所有管理器
        managers.destroy();

        console.log('Main module destroyed');
    } catch (error) {
        console.error('Failed to destroy main module:', error);
    }
}
