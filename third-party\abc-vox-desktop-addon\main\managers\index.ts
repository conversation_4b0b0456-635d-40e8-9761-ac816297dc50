/**
 * Managers 统一入口
 * 负责管理所有 manager 的初始化和销毁
 */

import { Manager as WindowManager } from './window-manager/manager';

/**
 * 管理器实例
 */
let windowManager: WindowManager | null = null;

/**
 * 初始化所有管理器
 * @param electron - Electron API 对象
 */
export function init(electron: any): void {
    if (!electron) {
        throw new Error('Electron API is required');
    }

    try {
        // 初始化窗口管理器
        windowManager = new WindowManager();
        windowManager.init(electron);

        console.log('All managers initialized successfully');
    } catch (error) {
        console.error('Failed to initialize managers:', error);
        throw error;
    }
}

/**
 * 销毁所有管理器
 */
export function destroy(): void {

    try {
        // 销毁窗口管理器
        if (windowManager) {
            windowManager.destroy();
            windowManager = null;
        }

        console.log('All managers destroyed');
    } catch (error) {
        console.error('Failed to destroy managers:', error);
    }
}

