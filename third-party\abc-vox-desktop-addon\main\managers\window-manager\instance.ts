/**
 * 简化版窗口实例类
 * 封装 BrowserWindow 并提供统一的窗口操作接口
 */

import { WindowSize, WindowPosition, WindowBounds } from '../../types/window-types';

export class Instance {
    public readonly id: string;
    public readonly browserWindow: any; // BrowserWindow
    public readonly options: any;

    private _isDestroyed = false;
    private _eventListeners: Map<string, Function> = new Map();

    constructor(browserWindow: any, options: any) {
        this.id = options.id;
        this.browserWindow = browserWindow;
        this.options = { ...options };

        // 绑定事件监听器
        this._bindEventListeners();
    }

    /**
     * 绑定窗口事件监听器
     */
    private _bindEventListeners(): void {
        // 窗口焦点事件
        const onFocus = async () => {
            if (this.options.lifecycle?.onFocus) {
                await this.options.lifecycle.onFocus(this);
            }
        };
        this.browserWindow.on('focus', onFocus);
        this._eventListeners.set('focus', onFocus);

        const onBlur = async () => {
            if (this.options.lifecycle?.onBlur) {
                await this.options.lifecycle.onBlur(this);
            }
        };
        this.browserWindow.on('blur', onBlur);
        this._eventListeners.set('blur', onBlur);

        // 窗口关闭事件
        const onClose = async (event: any) => {
            if (this.options.lifecycle?.onBeforeClose) {
                const shouldClose = await this.options.lifecycle.onBeforeClose(this);
                if (shouldClose === false) {
                    event.preventDefault();
                    return;
                }
            }
        };
        this.browserWindow.on('close', onClose);
        this._eventListeners.set('close', onClose);

        const onClosed = async () => {
            this._isDestroyed = true;
            if (this.options.lifecycle?.onAfterClose) {
                await this.options.lifecycle.onAfterClose(this);
            }
            // 窗口关闭后自动清理
            this._cleanup();
        };
        this.browserWindow.on('closed', onClosed);
        this._eventListeners.set('closed', onClosed);
    }

    /**
     * 清理事件监听器和引用
     */
    private _cleanup(): void {
        // 移除所有事件监听器
        for (const [eventName, listener] of this._eventListeners) {
            if (this.browserWindow && !this.browserWindow.isDestroyed()) {
                this.browserWindow.removeListener(eventName, listener);
            }
        }
        this._eventListeners.clear();

        // 清理生命周期钩子引用，防止内存泄漏
        if (this.options.lifecycle) {
            Object.keys(this.options.lifecycle).forEach(key => {
                delete this.options.lifecycle[key];
            });
        }
    }

    // ==================== 窗口显示/隐藏操作 ====================

    /**
     * 显示窗口
     */
    async show(): Promise<void> {
        if (this._isDestroyed) return;

        if (this.options.lifecycle?.onBeforeShow) {
            await this.options.lifecycle.onBeforeShow(this);
        }

        this.browserWindow.show();

        if (this.options.lifecycle?.onAfterShow) {
            await this.options.lifecycle.onAfterShow(this);
        }
    }

    /**
     * 隐藏窗口
     */
    async hide(): Promise<void> {
        if (this._isDestroyed) return;

        if (this.options.lifecycle?.onBeforeHide) {
            await this.options.lifecycle.onBeforeHide(this);
        }

        this.browserWindow.hide();

        if (this.options.lifecycle?.onAfterHide) {
            await this.options.lifecycle.onAfterHide(this);
        }
    }

    /**
     * 关闭窗口
     */
    async close(): Promise<void> {
        if (this._isDestroyed) return;

        // 主动清理，不等待 closed 事件
        this._cleanup();
        this.browserWindow.close();
    }

    /**
     * 销毁窗口实例（强制清理）
     */
    destroy(): void {
        if (this._isDestroyed) return;

        this._cleanup();
        this._isDestroyed = true;

        if (this.browserWindow && !this.browserWindow.isDestroyed()) {
            this.browserWindow.destroy();
        }
    }

    // ==================== 窗口大小和位置操作 ====================

    /**
     * 调整窗口大小
     */
    async resize(size: WindowSize): Promise<void> {
        if (this._isDestroyed) return;
        this.browserWindow.setSize(size.width, size.height);
    }

    /**
     * 移动窗口位置
     */
    async move(position: WindowPosition): Promise<void> {
        if (this._isDestroyed) return;
        this.browserWindow.setPosition(position.x, position.y);
    }

    /**
     * 设置窗口边界
     */
    setBounds(bounds: Partial<WindowBounds>): void {
        if (this._isDestroyed) return;
        const currentBounds = this.browserWindow.getBounds();
        const newBounds = { ...currentBounds, ...bounds };
        this.browserWindow.setBounds(newBounds);
    }

    /**
     * 获取窗口边界
     */
    getBounds(): WindowBounds {
        if (this._isDestroyed) return { x: 0, y: 0, width: 0, height: 0 };
        return this.browserWindow.getBounds();
    }

    // ==================== 窗口外观操作 ====================

    /**
     * 设置背景色
     */
    setBackgroundColor(color: string): void {
        if (this._isDestroyed) return;
        this.browserWindow.setBackgroundColor(color);
    }

    /**
     * 设置窗口标题
     */
    setTitle(title: string): void {
        if (this._isDestroyed) return;
        this.browserWindow.setTitle(title);
    }

    /**
     * 设置窗口置顶
     */
    setAlwaysOnTop(flag: boolean): void {
        if (this._isDestroyed) return;
        this.browserWindow.setAlwaysOnTop(flag);
    }

    /**
     * 设置窗口透明度
     */
    setOpacity(opacity: number): void {
        if (this._isDestroyed) return;
        this.browserWindow.setOpacity(opacity);
    }

    // ==================== 窗口状态操作 ====================

    /**
     * 聚焦窗口
     */
    focus(): void {
        if (this._isDestroyed) return;
        this.browserWindow.focus();
    }

    /**
     * 最小化窗口
     */
    minimize(): void {
        if (this._isDestroyed) return;
        this.browserWindow.minimize();
    }

    /**
     * 最大化窗口
     */
    maximize(): void {
        if (this._isDestroyed) return;
        this.browserWindow.maximize();
    }

    /**
     * 恢复窗口
     */
    restore(): void {
        if (this._isDestroyed) return;
        this.browserWindow.restore();
    }

    // ==================== 窗口状态查询 ====================

    /**
     * 检查窗口是否可见
     */
    isVisible(): boolean {
        if (this._isDestroyed) return false;
        return this.browserWindow.isVisible();
    }

    /**
     * 检查窗口是否最小化
     */
    isMinimized(): boolean {
        if (this._isDestroyed) return false;
        return this.browserWindow.isMinimized();
    }

    /**
     * 检查窗口是否最大化
     */
    isMaximized(): boolean {
        if (this._isDestroyed) return false;
        return this.browserWindow.isMaximized();
    }

    /**
     * 检查窗口是否全屏
     */
    isFullScreen(): boolean {
        if (this._isDestroyed) return false;
        return this.browserWindow.isFullScreen();
    }

    /**
     * 检查窗口是否聚焦
     */
    isFocused(): boolean {
        if (this._isDestroyed) return false;
        return this.browserWindow.isFocused();
    }

    /**
     * 检查窗口是否已销毁
     */
    isDestroyed(): boolean {
        return this._isDestroyed;
    }
}
