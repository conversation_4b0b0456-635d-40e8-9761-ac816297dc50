/**
 * 简化版窗口管理器
 * 负责窗口的创建、管理和基本操作
 */

import { Instance } from './instance';
import { WindowSize, WindowPosition } from '../../types/window-types';

export class Manager {
    private _windows = new Map<string, Instance>();
    private _electron: any;
    private _initialized = false;

    /**
     * 初始化窗口管理器
     * @param electron - Electron API 对象
     */
    init(electron: any): void {
        if (this._initialized) {
            console.warn('WindowManager already initialized');
            return;
        }

        this._electron = electron;

        // 直接在 electron 对象上挂载 API
        electron.windowManager = {
            createWindow: this.createWindow.bind(this),
            destroyWindow: this.destroyWindow.bind(this),
            showWindow: this.showWindow.bind(this),
            hideWindow: this.hideWindow.bind(this),
            resizeWindow: this.resizeWindow.bind(this),
            moveWindow: this.moveWindow.bind(this),
            updateWindow: this.updateWindow.bind(this),
            getWindow: this.getWindow.bind(this),
            getAllWindows: this.getAllWindows.bind(this),
            hasWindow: this.hasWindow.bind(this),
            getWindowCount: this.getWindowCount.bind(this),
            generateWindowId: this.generateWindowId.bind(this),
            closeAllWindows: this.closeAllWindows.bind(this)
        };

        this._initialized = true;
        console.log('WindowManager initialized and API mounted on electron.windowManager');
    }

    /**
     * 创建窗口
     */
    async createWindow(options: any): Promise<{ success: boolean; id?: string; error?: string }> {
        if (!this._initialized) {
            return { success: false, error: 'WindowManager not initialized' };
        }

        try {
            // 执行创建前钩子
            if (options.lifecycle?.onBeforeCreate) {
                await options.lifecycle.onBeforeCreate(options);
            }

            // 创建 BrowserWindow
            const browserWindow = new this._electron.BrowserWindow(this._buildBrowserWindowOptions(options));

            // 创建窗口实例
            const instance = new Instance(browserWindow, options);

            // 执行创建后钩子
            if (options.lifecycle?.onAfterCreate) {
                await options.lifecycle.onAfterCreate(instance);
            }

            // 注册窗口
            this._registerWindow(instance);

            // 加载 URL
            if (options.url) {
                await browserWindow.loadURL(options.url);
            }

            return { success: true, id: options.id };
        } catch (error) {
            console.error('Failed to create window:', error);
            return { success: false, error: (error as Error).message };
        }
    }

    /**
     * 销毁窗口
     */
    async destroyWindow(windowId: string): Promise<{ success: boolean; error?: string }> {
        try {
            const instance = this._windows.get(windowId);
            if (!instance) {
                return { success: false, error: 'Window not found' };
            }

            // 主动注销窗口，确保清理完成
            this._unregisterWindow(windowId);

            // 销毁窗口实例
            if (typeof (instance as any).destroy === 'function') {
                (instance as any).destroy();
            } else {
                await instance.close();
            }

            return { success: true };
        } catch (error) {
            console.error('Failed to destroy window:', error);
            return { success: false, error: (error as Error).message };
        }
    }

    /**
     * 显示窗口
     */
    async showWindow(windowId: string): Promise<{ success: boolean; error?: string }> {
        try {
            const instance = this._windows.get(windowId);
            if (!instance) {
                return { success: false, error: 'Window not found' };
            }

            await instance.show();
            return { success: true };
        } catch (error) {
            console.error('Failed to show window:', error);
            return { success: false, error: (error as Error).message };
        }
    }

    /**
     * 隐藏窗口
     */
    async hideWindow(windowId: string): Promise<{ success: boolean; error?: string }> {
        try {
            const instance = this._windows.get(windowId);
            if (!instance) {
                return { success: false, error: 'Window not found' };
            }

            await instance.hide();
            return { success: true };
        } catch (error) {
            console.error('Failed to hide window:', error);
            return { success: false, error: (error as Error).message };
        }
    }

    /**
     * 调整窗口大小
     */
    async resizeWindow(windowId: string, size: WindowSize): Promise<{ success: boolean; error?: string }> {
        try {
            const instance = this._windows.get(windowId);
            if (!instance) {
                return { success: false, error: 'Window not found' };
            }

            await instance.resize(size);
            return { success: true };
        } catch (error) {
            console.error('Failed to resize window:', error);
            return { success: false, error: (error as Error).message };
        }
    }

    /**
     * 移动窗口位置
     */
    async moveWindow(windowId: string, position: WindowPosition): Promise<{ success: boolean; error?: string }> {
        try {
            const instance = this._windows.get(windowId);
            if (!instance) {
                return { success: false, error: 'Window not found' };
            }

            await instance.move(position);
            return { success: true };
        } catch (error) {
            console.error('Failed to move window:', error);
            return { success: false, error: (error as Error).message };
        }
    }

    /**
     * 更新窗口属性
     */
    async updateWindow(windowId: string, updates: any): Promise<{ success: boolean; error?: string }> {
        try {
            const instance = this._windows.get(windowId);
            if (!instance) {
                return { success: false, error: 'Window not found' };
            }

            // 更新 URL
            if (updates.url && updates.url !== instance.options.url) {
                await instance.browserWindow.loadURL(updates.url);
            }

            // 更新窗口属性
            if (updates.title) {
                instance.setTitle(updates.title);
            }

            if (updates.backgroundColor) {
                instance.setBackgroundColor(updates.backgroundColor);
            }

            if (updates.alwaysOnTop !== undefined) {
                instance.setAlwaysOnTop(updates.alwaysOnTop);
            }

            // 更新大小和位置
            if (updates.width || updates.height) {
                const size: WindowSize = {
                    width: updates.width || instance.options.width!,
                    height: updates.height || instance.options.height!
                };
                await instance.resize(size);
            }

            if (updates.x !== undefined || updates.y !== undefined) {
                const position: WindowPosition = {
                    x: updates.x !== undefined ? updates.x : instance.getBounds().x,
                    y: updates.y !== undefined ? updates.y : instance.getBounds().y
                };
                await instance.move(position);
            }

            // 更新选项对象
            Object.assign(instance.options, updates);

            return { success: true };
        } catch (error) {
            console.error('Failed to update window:', error);
            return { success: false, error: (error as Error).message };
        }
    }

    /**
     * 获取窗口信息
     */
    getWindow(windowId: string): any {
        const instance = this._windows.get(windowId);
        if (!instance) {
            return null;
        }

        return {
            id: instance.id,
            options: instance.options,
            isVisible: instance.isVisible(),
            isDestroyed: instance.isDestroyed(),
            bounds: instance.getBounds()
        };
    }

    /**
     * 获取所有窗口信息
     */
    getAllWindows(): any[] {
        return Array.from(this._windows.values()).map(instance => ({
            id: instance.id,
            options: instance.options,
            isVisible: instance.isVisible(),
            isDestroyed: instance.isDestroyed(),
            bounds: instance.getBounds()
        }));
    }

    /**
     * 检查窗口是否存在
     */
    hasWindow(windowId: string): boolean {
        return this._windows.has(windowId);
    }

    /**
     * 获取窗口数量
     */
    getWindowCount(): number {
        return this._windows.size;
    }

    /**
     * 生成唯一窗口ID
     */
    generateWindowId(prefix: string = 'window'): string {
        let id: string;
        let counter = 1;
        
        do {
            id = `${prefix}_${Date.now()}_${counter}`;
            counter++;
        } while (this._windows.has(id));
        
        return id;
    }

    /**
     * 关闭所有窗口
     */
    async closeAllWindows(excludeWindowIds?: string[]): Promise<{ success: boolean; count: number; error?: string }> {
        try {
            const windowsToClose = Array.from(this._windows.values()).filter(window => 
                !excludeWindowIds?.includes(window.id)
            );

            let closedCount = 0;
            const closePromises = windowsToClose.map(async (window) => {
                try {
                    await window.close();
                    closedCount++;
                } catch (error) {
                    console.error(`Failed to close window ${window.id}:`, error);
                }
            });

            await Promise.all(closePromises);
            return { success: true, count: closedCount };
        } catch (error) {
            console.error('Failed to close all windows:', error);
            return { success: false, count: 0, error: (error as Error).message };
        }
    }

    /**
     * 构建 BrowserWindow 选项
     */
    private _buildBrowserWindowOptions(options: any): any {
        return {
            width: options.width,
            height: options.height,
            x: options.x,
            y: options.y,
            minWidth: options.minWidth,
            minHeight: options.minHeight,
            maxWidth: options.maxWidth,
            maxHeight: options.maxHeight,
            resizable: options.resizable !== false,
            minimizable: options.minimizable !== false,
            maximizable: options.maximizable !== false,
            closable: options.closable !== false,
            alwaysOnTop: options.alwaysOnTop || false,
            modal: options.modal || false,
            parent: options.parent ? this._windows.get(options.parent)?.browserWindow : undefined,
            backgroundColor: options.backgroundColor || '#ffffff',
            frame: options.frame !== false,
            transparent: options.transparent || false,
            show: options.show !== false,
            center: options.center || false,
            title: options.title || '',
            icon: options.icon,
            webPreferences: options.webPreferences || {
                nodeIntegration: true,
                contextIsolation: false
            }
        };
    }

    /**
     * 注册窗口
     */
    private _registerWindow(instance: Instance): void {
        this._windows.set(instance.id, instance);

        // 使用弱引用避免循环引用，监听窗口关闭事件
        const windowId = instance.id;
        const onClosed = () => {
            this._unregisterWindow(windowId);
        };

        instance.browserWindow.on('closed', onClosed);

        // 存储清理函数到实例上，避免在 Manager 中持有对 instance 的强引用
        (instance as any)._managerCleanup = () => {
            if (instance.browserWindow && !instance.browserWindow.isDestroyed()) {
                instance.browserWindow.removeListener('closed', onClosed);
            }
        };
    }

    /**
     * 注销窗口
     */
    private _unregisterWindow(windowId: string): void {
        const instance = this._windows.get(windowId);
        if (instance) {
            // 执行管理器级别的清理
            if ((instance as any)._managerCleanup) {
                (instance as any)._managerCleanup();
                delete (instance as any)._managerCleanup;
            }

            // 从映射中移除
            this._windows.delete(windowId);
        }
    }

    /**
     * 销毁窗口管理器
     */
    destroy(): void {
        try {
            // 强制销毁所有窗口实例，避免异步操作
            for (const [windowId, instance] of this._windows) {
                try {
                    // 执行管理器级别的清理
                    if ((instance as any)._managerCleanup) {
                        (instance as any)._managerCleanup();
                        delete (instance as any)._managerCleanup;
                    }

                    // 销毁窗口实例
                    if (typeof (instance as any).destroy === 'function') {
                        (instance as any).destroy();
                    } else if (!instance.isDestroyed()) {
                        instance.browserWindow.destroy();
                    }
                } catch (error) {
                    console.error(`Error destroying window ${windowId}:`, error);
                }
            }

            // 清理内部状态
            this._windows.clear();
            this._initialized = false;

            // 清理 electron 对象上的引用，断开循环引用
            if (this._electron && this._electron.windowManager) {
                delete this._electron.windowManager;
            }

            this._electron = null;

            console.log('WindowManager destroyed');
        } catch (error) {
            console.error('Error during WindowManager destruction:', error);
        }
    }
}
