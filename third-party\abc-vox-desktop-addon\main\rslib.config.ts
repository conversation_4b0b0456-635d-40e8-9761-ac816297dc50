import { defineConfig } from '@rslib/core';

export default defineConfig({
    source: {
        entry: {
            index: './index.ts',
        },
    },
    lib: [
        {
            format: 'esm',
            output: {
                distPath: {
                    root: './dist/esm',
                },
            },
        },
        {
            format: 'cjs',
            output: {
                distPath: {
                    root: './dist/cjs',
                },
            },
        },
    ],
});