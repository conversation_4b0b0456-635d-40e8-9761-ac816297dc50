/**
 * 简化版窗口管理相关的类型定义
 */

export interface WindowSize {
    width: number;
    height: number;
}

export interface WindowPosition {
    x: number;
    y: number;
}

export interface WindowBounds extends WindowPosition, WindowSize {}

export interface LifecycleHooks {
    onBeforeCreate?: (options: any) => Promise<void> | void;
    onAfterCreate?: (instance: any) => Promise<void> | void;
    onBeforeShow?: (instance: any) => Promise<void> | void;
    onAfterShow?: (instance: any) => Promise<void> | void;
    onBeforeHide?: (instance: any) => Promise<void> | void;
    onAfterHide?: (instance: any) => Promise<void> | void;
    onBeforeClose?: (instance: any) => Promise<boolean> | boolean;
    onAfterClose?: (instance: any) => Promise<void> | void;
    onFocus?: (instance: any) => Promise<void> | void;
    onBlur?: (instance: any) => Promise<void> | void;
}
