import {electron} from "./jsaipienv";
import {electronRequire} from "./common/compiler";

const {ipc<PERSON><PERSON><PERSON>} = electronRequire("electron");


export interface UpgradeInfo {
    version: string;
    releaseNotes: string;
    updateType: "exe" | "jsbundle"
}

interface IUpgradeListener {
    updateAvailable(updateInfo: UpgradeInfo): void
}


export interface UpgradeInfo {
    version: string;
    releaseNotes: string;
    updateType: "exe" | "jsbundle"
}

class AbcUpgrade {
    private _listener?: IUpgradeListener;

    constructor() {
        ipcRenderer.on('__upgrade_update_available', (event, info: UpgradeInfo) => {
            this._listener?.updateAvailable(info);
        });
    }


    /**
     * 设置升级模块回调，用于监听升级相关事件
     */
    public setUpgradeListener(listener: IUpgradeListener) {
        this._listener = listener;
        this.getUpgradeInfo().then(info => {
            if (info) {
                this._listener?.updateAvailable(info);
            }
        });
    }


    /**
     * 检查升级
     */
    public async checkUpgrade(): Promise<UpgradeInfo | undefined> {
        return electron.remote.app.abcUpdater.checkUpgrade().catch(ignored => undefined);
    }

    /**
     * 获取升级信息
     */
    public async getUpgradeInfo(): Promise<UpgradeInfo> {
        return electron.remote.app.abcUpdater.upgradeInfo;
    }

    /**
     * 触发升级
     */
    public async upgrade() {
        electron.remote.app.abcUpdater.upgrade();
    }
}

electron.abcUpgrade = new AbcUpgrade();
