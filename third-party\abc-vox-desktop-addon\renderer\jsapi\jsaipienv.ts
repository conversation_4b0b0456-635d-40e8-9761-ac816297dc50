import {windowAny, electronRequire} from "./common/compiler";

const path = require('path');
const windowModule = (windowAny as any).module;
let extraNodeModules = '';

const execPath = process.execPath;
let nodeModulesIndex = -1;
//Windows 本地调试
if (path.basename(execPath) === 'electron.exe') {
    extraNodeModules = path.join(execPath, '../../..')
}
// mac 本地调试
else if ("darwin" === process.platform && (nodeModulesIndex = execPath.indexOf("node_modules/electron/dist")) > 0) {
    extraNodeModules = execPath.substring(0, nodeModulesIndex) + "/node_modules";
} else {
    //打包后运行
    extraNodeModules = path.join(path.dirname(execPath), 'resources/app.asar/node_modules');
    extraNodeModules = path.join((process as any).resourcesPath, 'app.asar/node_modules');
}

if (windowModule.paths.indexOf(extraNodeModules) === -1) {
    windowModule.paths.push(extraNodeModules);
}


if (!electronRequire('electron').remote) {
    electronRequire('electron').remote = electronRequire('@electron/remote');
}

const remote = electronRequire('electron').remote
const ipcRenderer = electronRequire('electron').ipcRenderer;


function handleKeyboardShut() {
    windowAny.addEventListener('keydown', (e: any) => {
            let event = e || windowAny.event;
            if (event.keyCode === 123) { // F12
                let mainWindow = remote.BrowserWindow.getFocusedWindow();
                if (mainWindow) {
                    if (mainWindow.getBrowserView() && mainWindow.getBrowserView().webContents) {
                        mainWindow.getBrowserView().webContents.openDevTools();
                    } else if (mainWindow.webContents) {
                        mainWindow.webContents.openDevTools({mode: "detach"});
                    }
                }
                e.stopPropagation();
            }
        }
    );
}

//1.4.2后修改为走preload方式后，需要setTimeout异步一下，不然mousewheel事件监听失效
setTimeout(() => {
    handleKeyboardShut();
}, 0);

windowAny.electronFlag = true;
windowAny.remote = remote;
windowAny.ipcRenderer = ipcRenderer;
windowAny.electronVersion = "1.0";

windowAny.electron = {
    // electronLogger: remoteAsAny.app.logger,
    logger: remote ? (remote as any).app.logger : undefined,
    remote: remote,
    electronVersion: '1.0',
    ipcRenderer: ipcRenderer,
};

try {
    //这里以require方式引入，在registerJSAPForBrowserWindow注入js api时无效，registerJSAPForBrowserWindow将会在后续版本的删除
    //向前端注入客户端打包时间等相关信息
    // const {BuildConfig} = require("../../build-config");
    windowAny.electron.buildInfo = windowAny.electron.remote.app.buildInfo;
} catch (e) {
}

interface AbcElectron {
    logger: {
        info: (log: string) => void;
        log: (log: string) => void;
    };
    remote: any;  //Electron.Remote
    electronVersion: string;
    ipcRenderer: any;
    window: any; //JSApiWindow
    appConfig: any;//AppConfigJSApi
    downloadManager: any; //DownloadManager
    appSharedPreferences: any; //AppSharedPreferences
    abcUpgrade: any;
    global: any;
}


const electron = windowAny.electron as AbcElectron;
electron.remote = remote;

export {electron}


