import {electron} from "./jsaipienv";
import {windowAny} from "./common/compiler";

const {remote} = electron;

const {BrowserWindow} = remote;

class JSApiWindow {
    /**
     *
     * @param url 要打开的url
     * @param options {{
     *     modal?:boolean,
     *     minimizable?:boolean,
     *     resizable?:boolean,
     *     width?:boolean,
     *     height?:boolean,
     *     webPreferences?: {
     *         zoomFactor?:boolean,
     *     }
     * }}json对象
     * @param callback 回调接口=>function(eventName, params)
     *             eventName: string
     *                  'close': //窗口关闭
     *      e.g:
     *      {
     *          modal: true, //模态对话框形态,默认为true
     *          width: 1024, // 窗口宽度,默认为1024
     *          height: 768, // 窗口高度,默认768
     *          openDevTools: false, //是否打开调试窗口，默认false
     *          resizable:false, //是否允许缩放default false
     *          minimizable:false, //是否允许最小化,默认false
     *          nodeIntegration:true //是否开店node环境,
     *          webPreferences: {
     *
     *          }
     *      }
     *      e.g:
     *
     *     windowAny.electron.windowAny.openURL('http://www.baidu.com', function(eventName, params) {
     *          console.log('eventName=' + eventName);
     *     }, {
     *         width:1024,
     *         height:768
     *     });
     */
    public openURL(url: string, callback: Function, options: any) {
        console.log("begin openURL");
        // 或者从渲染进程中使用 `remote`.
        let top = remote.getCurrentWindow();
        if (!options)
            options = {};
        let {
            nodeIntegration = true,
            contextIsolation = false,
            modal = true,
            resizable = true,
            minimizable = true,
            width = 1024,
            height = 1024,
            webPreferences = {}
        } = options ?? {};
        let windowOpts = {
            ...options,
            parent: top,
            modal: modal,
            resizable: resizable,
            minimizable: minimizable,
            width: width,
            height: height,
            webPreferences: {
                nodeIntegration: nodeIntegration,
                contextIsolation: contextIsolation,
                webviewTag: true,
                enableRemoteModule: true,
                preload: (remote.app as any).jsApiFile,
                ...webPreferences,
            },
            show: true,
        };

        let child = new BrowserWindow(windowOpts);
        let self = this;
        child.loadURL(url).then(() => {
            child.on('close', function () {
                console.log("openURL fire close event, callback=" + callback);
                if (callback) {
                    callback('close');
                }
            });
        }).catch(error => {
            console.log("openURL failed error=" + error);
            child.close();
            self.openURL(url, callback, options); //try again
        });


        let path = (remote.app as any).taskBarIconPath;
        //设置任务栏图标
        child.setAppDetails && child.setAppDetails({
            appId: 'cn.abcyun.app.desktop-pc', //这里app id需要和package.json里的id不一样,否则会强制使用应用图标(icon.png),设置不生效
            appIconPath: path,
            appIconIndex: 0,
        });

        //windows only, mac上设置ico图标会报异常
        //顶部左上角图标
        if (process.platform === 'win32') {
            child.setIcon(path);
        }

        if (options && options.openDevTools)
            child.webContents.openDevTools();
    }

    /**
     * 设置是否允许关闭窗口
     * @param enable
     */
    public setCloseable(enable: boolean): void {
        const currentWin = remote.getCurrentWindow();
        if (currentWin)
            currentWin.setClosable(enable);
    }

    public electronReload(url: string): void {
        let mainWindow = BrowserWindow.getFocusedWindow();
        if (url && url !== '') {
            mainWindow && mainWindow.loadURL(url);
        } else {
            mainWindow && mainWindow.reload();
        }
    }
}


electron.window = new JSApiWindow();

//兼容之前的调用方式
windowAny.openURL = windowAny.electron.window.openURL;
windowAny.electronReload = windowAny.electron.window.electronReload;


