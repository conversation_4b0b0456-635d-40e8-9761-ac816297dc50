<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>网络异常</title>
</head>
<body>
    <div>连接ABC服务网络异常</div>
    <div id="abc-network">
       
    </div>

    <script>
        const TAG = "NetworkHelper";
        const ipcRenderer = window.electron.ipcRenderer;
        document.getElementById("abc-network").addEventListener("click", () => {
            ipcRenderer.send("start-abc-network-test");
        })

        ipcRenderer.on(`${TAG}:abc-network`, (event, arg) => {
            document.getElementById("abc-network").innerHTML += JSON.stringify(arg) + '<br>';
        })
    </script>
</body>
</html>