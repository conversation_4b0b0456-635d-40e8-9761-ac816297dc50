<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>alert</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
            width: 100%;
            position: fixed;
            background-color: rgba(0, 0, 0, 0.5);
        }
        
        .alert-container {
            background-color: white;
            border-radius: 5px;
            min-width: 360px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            position: relative;
        }

        .alert-content {
            padding: 24px;
        }
        
        .alert-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .alert-icon {
            color: #ff9800;
            margin-right: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .alert-title {
            font-size: 16px;
            font-weight: bold;
            color: #000;
            margin: 0;
            line-height: 24px;
        }
        
        .alert-content-text {
            font-size: 14px;
            color: #000;
            line-height: 20px;
            padding-left: 24px;
        }
        
        .alert-footer {
            display: flex;
            justify-content: flex-end;
            padding: 0 16px 16px;
        }
        
        .alert-button {
            background-color: #1ec761;
            color: white;
            border: none;
            border-radius: 4px;
            min-width: 64px;
            font-size: 14px;
            cursor: pointer;
            outline: none;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 32px;
            line-height: 1;
        }
        
        .alert-button:hover {
            background-color: #23cf67;;
        }

        .alert-button:active {
            background-color: #08a446;
        }

        .close-btn {
            height: 40px;
            width: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            right: 4px;
            top: 4px;
            border-radius: 4px;
            cursor: pointer;
        }

        .close-btn .icon {
            fill: #aab4bf;
        }

        .close-btn:hover {
            background: #eaedf1;
        }

        .close-btn:hover .icon {
            fill: #7a8794;
        }

        .close-btn:active {
            background: #e0e5ee;
        }
        
        .close-icon {
            height: 20px;
            width: 20px;
        }
    </style>
</head>
<body>
    <div class="alert-container">
        <div class="close-btn">
            <div class="close-icon">
                <svg t="1742176145359" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="87455" id="mx_n_1742176145359" width="20" height="20"><path d="M742.326857 215.186286a47.542857 47.542857 0 0 1 61.878857 71.826285L579.145143 512l225.060571 225.060571a47.542857 47.542857 0 0 1-61.878857 71.826286l-5.339428-4.608-225.060572-225.060571-224.914286 225.060571-5.339428 4.608a47.542857 47.542857 0 0 1-61.878857-71.826286L444.708571 511.926857 219.794286 287.012571a47.542857 47.542857 0 0 1 61.878857-71.826285l5.339428 4.608L512 444.708571l225.060571-224.914285z" p-id="87456"></path></svg>
            </div>
        </div>
        <div class="alert-content">
            <div class="alert-header">
                <div class="alert-icon">
                    <svg t="1741856117540" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="44486" width="16" height="16"><path d="M548.498286 68.608a73.142857 73.142857 0 0 1 26.916571 26.843429l429.348572 745.764571A73.142857 73.142857 0 0 1 941.348571 950.857143H82.651429a73.142857 73.142857 0 0 1-63.414858-109.641143l429.348572-745.764571A73.142857 73.142857 0 0 1 548.571429 68.608zM512 705.097143a57.782857 57.782857 0 0 0-58.514286 57.051428c0 31.670857 26.331429 57.051429 58.514286 57.051429a59.245714 59.245714 0 0 0 41.252571-16.530286 56.32 56.32 0 0 0 17.261715-40.521143 57.782857 57.782857 0 0 0-58.514286-57.051428zM512 336.457143a57.782857 57.782857 0 0 0-58.514286 57.051428l23.478857 203.117715c-0.073143 29.257143 13.750857 55.734857 35.035429 55.734857 21.138286 0 34.889143-26.112 35.108571-56.32l23.332572-201.142857A58.002286 58.002286 0 0 0 512 336.457143z" p-id="44487" fill="#ff9933"></path></svg>
                </div>
                <div class="alert-title">删除提示</div>
            </div>
            <div class="alert-content-text"></div>
        </div>
        <div class="alert-footer">
            <button class="alert-button">知道了</button>
        </div>
    </div>

    <script>
         const urlParams = new URLSearchParams(window.location.search);
         const sendId = urlParams.get('sendId');
         const ipcRenderer = window.electron.ipcRenderer;
         document.querySelector('.alert-title').textContent = urlParams.get('title');
         document.querySelector('.alert-content-text').textContent = urlParams.get('content');

         // 添加关闭事件
         const onClose = () => {
            ipcRenderer.send(sendId, {
                type: 'close'
            });
         }

         document.querySelector('.close-btn').addEventListener('click', onClose);
         document.querySelector('.alert-button').addEventListener('click', onClose);
    </script>
</body>
</html>