<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <style>
        body {
            background: #FFF9FAFF;
            width:100%;
            height:100vh;
            margin: 0 0;
            background-size: cover;
            backdrop-filter: blur(4px);
            background-position: center;
        }

        .background-mask {
            width:100%;
            height:100vh;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .centered-window {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 560px;
            height: 316px;
            background: rgba(255, 255, 255, 0.85);
            backdrop-filter: blur(5px);
            transform: translate(-50%, -50%);

            border-radius: 16px;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .logo {
            position: relative;
            width: 178px;
            height: 24px;
            margin-top: 72px;
        }


        .progress-bar {
            width: 160px;
            height: 4px;
            background: #EAEDF1;
            border-radius: 2px;
            overflow: hidden;
        }

        .progress-bar div {
            height: 100%;
            width: 1%;
            background: #5199F8;
            border-radius: 2px;
        }

        .update-error {
            margin-top: 5px;
            font-size: 10px;
            color: red;
            max-width: 200px;
            max-height: 45px;
            overflow: hidden;
            visibility: hidden;
        }

        .t1 {
            font-size: 18px;
            color: black;
            margin-bottom: 48px;
        }

        .copyright {
            position: absolute;
            bottom: 0;
            margin-bottom: 24px;
            font-size: 10px;
            line-height: 10px;
            color: #7A8794;
            //margin-bottom: 24px;
        }

        .updating-text {
            color: #7A8794;
            margin-top: 8px;
            font-size: 12px;
        }

        .suffix {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 142px;
            height: 142px;
        }
    </style>
</head>
<body>
<div id="backgroundMask" class="background-mask"></div>
<div class="centered-window">
    <div class="container">
        <img src="./static/image/splash-logo.png" class="logo">
        <p class="t1">用科技和仁心助力基层医疗发展</p>
        <div class="progress-bar">
            <div></div>
        </div>
        <p class="updating-text">正在更新...</p>
        <p class="update-error"></p>
        <p class="copyright">Copyright ©2025 . All Rights Reserved</p>
        <img src="./static/image/splash-right-suffix.png" class="suffix">
    </div>
</div>
</body>

<script>
    const fs = window.require('fs');
    const bgFile = window.require('path').join(electron.remote.app.getPath('userData'), 'tmp/background-screenshot.png');
    let bgFileExist = false;
    let stat = null;
    try {
        stat = fs.statSync(bgFile);
        bgFileExist = true;
    } catch (e) {
    }
    if (bgFileExist) {
        const bg = bgFile.replaceAll("\\", "/");
        document.body.style['background-image'] = `url('${bg}')`
    }
    else {
        backgroundMask.style['display']='none';
    }



    let preCurrentTotalDownloadBytes = 0;
    const ipcRenderer = require('electron').ipcRenderer;
    ipcRenderer.on('__update_offline_bundle_update__', function (event, options) {
        let {
            bundleCount,
            bundleIndex,
            bundleName,
            currentBundleDownloadBytes,
            currentBundleTotalBytes,
            currentTotalDownloadBytes,
            totalBytes,
            successCount,
            failCount,
            finish,
            error,
        } = options;
        if (preCurrentTotalDownloadBytes > currentTotalDownloadBytes) {
            currentTotalDownloadBytes = preCurrentTotalDownloadBytes;
        }
        preCurrentTotalDownloadBytes = currentTotalDownloadBytes;

        if (error && error.length > 0) {
            let errorEl = document.querySelector('.update-error');
            errorEl.textContent = '更新出错: ' + error[error.length - 1];
            errorEl.style.visibility = 'visible';
        }

        const progress = ((currentTotalDownloadBytes / totalBytes) * 100).toFixed(0);
        let progressEl = document.querySelector('.progress-bar div');
        progressEl.style.width = `${progress}%`;
    });

</script>
</html>
