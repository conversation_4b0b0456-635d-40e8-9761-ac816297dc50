<style scoped>
.line {
  margin-bottom: 20px;
}
</style>

<template>
  <div>
    <div style="margin-bottom: 20px;">当前分区: {{ region }} 环境: {{ grayEnv }}
      <button
          v-on:click="updateNow()">更新
      </button>
    </div>
  </div>
  <div v-for="plugin in pluginInfos" class="line">
    <div>名称：{{ plugin.name }} <a href="javascript:void(0)" v-on:click="openBundleDir(plugin.rootDir)">打开目录</a>
    </div>
    <div>版本：{{ plugin.version }}</div>
    <div>构建时间：{{
        plugin.buildTime ? new Date(Number(plugin.buildTime)).toLocaleString() : null
      }}
    </div>
    <div>更新时间：{{
        plugin.updateTime ? new Date(Number(plugin.updateTime)).toLocaleString() : null
      }}
    </div>
  </div>
</template>
<script>
import {useRouter} from 'vue-router'

export default {
  name: '插件信息',

  setup() {
    const router = useRouter()
    const toHome = (() => {
      router.push({
        name: 'home'
      })
    })
    return {
      toHome
    }
  },

  components: {},

  data() {
    return {
      /**
       * @type {}
       */
      pluginInfos: [],
      region: '',
      grayEnv: ''
    }
  },

  methods: {
    openBundleDir: function (dir) {
      window.remote.shell.showItemInFolder(dir);
    },
    updateNow: function () {
      window.electron.remote.app.offlineBundler.checkPluginsUpdateWithCurrentGrayEnv(true)
          .then(async rsp => {
            await window.electron.remote.app.offlineBundler.updateLocalOfflineBundle(true);
            this.updateInternal();
          })
    },

    updateInternal: function () {
      window.electron.remote.app.offlineBundler.getOfflineBundleInfoList().then(result => {
        this.pluginInfos = result.list;
        this.region = result.region;
        this.grayEnv = result.grayEnv;
      });
    }
  },
  beforeCreate() {
    document.title = `前端离线资源包信息`;
  },
  created() {
    this.updateInternal();
  }
}
</script>
<style scoped>
</style>
