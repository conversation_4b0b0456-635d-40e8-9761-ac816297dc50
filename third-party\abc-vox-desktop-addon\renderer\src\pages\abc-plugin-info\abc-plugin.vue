<style scoped>
/* Tooltip container */
.tooltip {
  position: relative;
  display: inline-block;
}

/* Tooltip text */
.tooltip .tooltiptext {
  visibility: hidden;
  width: 200px;
  bottom: 100%;
  left: 50%;
  margin-left: -60px;
  background-color: black;
  color: #fff;
  text-align: center;
  padding: 5px 0;
  border-radius: 6px;

  /* Position the tooltip text - see examples below! */
  position: absolute;
  z-index: 1;
}

/* Show the tooltip text when you mouse over the tooltip container */
.tooltip:hover .tooltiptext {
  visibility: visible;
}

.section {
  border: black solid 1px;
  margin: 15px 0;
}

.sectionTitle {
  margin: 5px 5px;
  font-weight: bold;
  font-size: 16px;
}

.item {
  margin-bottom: 20px;
}

.line {
  margin-bottom: 5px;
}
</style>

<template>
  <div v-for="plugin in pluginInfos" class="item">
    <div>
      <div class="line">
        <span class="pluginNameTitle">名字：</span>
        <span class="pluginNamepluginName">{{ plugin.name }}</span>
      </div>
      <div class="line">
        <span class="pluginNameTitle">构建时间：</span>
        <span class="pluginNamepluginName">{{ new Date(Number(plugin.buildTime)).toLocaleString() }}</span>
      </div>

      <div class="line">
        <span class="pluginNameTitle">tag：</span>
        <span class="pluginNamepluginName">{{ plugin.repoTag }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import {useRouter} from 'vue-router'

export default {
  name: '插件信息',

  setup() {
    const router = useRouter()
    const toHome = (() => {
      router.push({
        name: 'home'
      })
    })
    return {
      toHome
    }
  },

  components: {},

  data() {
    return {
      /**
       * @type {}
       */
      pluginInfos: []

    }
  },

  methods: {},
  beforeCreate() {
    document.title = `插件信息`;

    window.electron.remote.app.getAbcPluginList().then(list => {
      console.log(`pluginInfos= `, list);
      this.pluginInfos = list;
    });
  }
}
</script>
<style scoped>
</style>
