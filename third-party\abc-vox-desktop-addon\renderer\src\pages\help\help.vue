<style>
.item {
  margin-bottom: 20px;
}
</style>

<template>
  <div class="item">
    <a href="javascript:void(0)" v-on:click="clearCache">清空缓存并重启</a>
  </div>

  <div class="item">
    <router-link
        to="/setting"
        v-slot="{ href, route, navigate, isActive, isExactActive }"
    >设置
    </router-link>
  </div>

  <div class="item" v-if="isWin32">
    <a href="javascript:void(0)" v-on:click="exitSocialSecurityPrivateNetMode">退出专网模式</a>
  </div>


  <div class="item">
    <a href="javascript:void(0)" v-on:click="downloadLatestApp(appUrl,
     'downloadLatestAppProgress')">下载安装最新客户端{{ downloadLatestAppProgress }}</a>
  </div>

  <div class="item">
    <router-link
        to="/abc-plugin"
        v-slot="{ href, route, navigate, isActive, isExactActive }"
    >本地插件信息
    </router-link>
  </div>
  <div class="item">
    <router-link
        to="/abc-offline-bundle"
        v-slot="{ href, route, navigate, isActive, isExactActive }"
    >前端离线资源信息
    </router-link>
  </div>
</template>
<script>
import {useRouter} from 'vue-router'

export default {
  setup() {
    const router = useRouter()
    const toHome = (() => {
      router.push({
        name: 'home'
      })
    })
    return {
      toHome
    }
  },

  name: 'App',
  components: {},
  methods: {
    clearCache: function () {
      window.remote.app.clearCache();
    },
    
    downloadLatestApp: async function (url, processKey) {
      const tmpFile = `${window.remote.app.getPath('userData')}/tmp/shebao${url.substring(url.lastIndexOf("/"))}`;
      const startTime = new Date().getTime();
      const downloader = await window.electron.downloadManager.downloadFile({
        url: url,
        file: tmpFile,
        onProcess: (currentBytes, totalBytes) => {
          console.log(`正在下载：${(currentBytes / totalBytes * 100).toFixed(2)}%`);
          const timeInSeconds = Math.max((new Date().getTime() - startTime) / 1000, 1);
          this[processKey] = `(正在下载：${(currentBytes / totalBytes * 100).toFixed(2)}%, ${(currentBytes / 1000 / timeInSeconds).toFixed(2)}kb/s)`;
        },
        onComplete: () => {
          console.log(`下载完成`);
        }
      });


      const downloadRsp = await downloader.waitComplete();

      const installer = window.require("child_process").spawn(downloadRsp.fullPath, {detached: true});


      installer.on("close", (code, signal) => {
        console.log(`安装完成`);

      });

      installer.stdout.on('data', (data) => {
        console.log(`${data}`);
      });

      installer.stderr.on('error', (data) => {
        console.error(`${data}`);
      });
    }
  },


  data() {
    return {
      isWin32: window.process.platform === 'win32',
      downloadLatestAppProgress: "",
      appUrl: `https://cis-static-common.oss-cn-shanghai.aliyuncs.com/apks/abc_pc/abcyun-desktop-win-${window.process.arch === 'x64' ? 'x64-' : ''}latest.exe`
    }
  },

  beforeCreate() {
    document.title = `帮助页面`;
  }
}
</script>
<style scoped>
</style>
