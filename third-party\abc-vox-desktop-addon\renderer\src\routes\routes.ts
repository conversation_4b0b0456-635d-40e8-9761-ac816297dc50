/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2021/3/19
 *
 * @description
 */

import {createRouter, createWebHashHistory, RouteRecordRaw} from "vue-router";

const routes: RouteRecordRaw[] = [
    {
        path: "/help",
        component: () => import("../pages/help/help.vue"),
    },
    {
        path: "/setting",
        component: () => import("../pages/setting/setting.vue"),
    },
    {
        path: "/abc-plugin",
        component: () => import("../pages/abc-plugin-info/abc-plugin.vue"),
    },
    {
        path: "/abc-offline-bundle",
        component: () => import("../pages/abc-offline-bundle/abc-offline-bundle.vue"),
    },
]


export const router = createRouter({
    history: createWebHashHistory(),
    routes: routes
})
