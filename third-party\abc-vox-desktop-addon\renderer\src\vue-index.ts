const path = window.require('path');
let appAsarPath  = path.resolve((window as any).remote.app.getAppPath(), 'node_modules');

if (window.module && window.module.paths && window.module.paths.indexOf(appAsarPath) < 0) {
    window.module.paths.push(appAsarPath)
}

import {createApp} from 'vue';
import {router} from './routes/routes';


import App from './vue-index.vue';

const app = createApp(App)
app.use(router).mount('#app');

