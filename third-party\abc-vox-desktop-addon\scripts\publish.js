const {AbcDesktopPluginBuildTool} = require("abc-fed-build-tool");
const path = require("path");
const axios = require("axios");
const OSS = require("abc-fed-build-tool/lib/oss-info");
const AliOSS = require("ali-oss");
const fs = require("fs");

const archiver = require('archiver');
const encrypted = require('archiver-zip-encrypted');

const kBuildEnv = process.env.BUILD_ENV || "dev"
const kSourceMapPassword = "r5Y!xqT4aq%TK#yh";

function aliPutObject(...params) {
    const useOSS = process.env.BUILD_ENV === 'gray' || process.env.BUILD_ENV === 'test';
    const aliOSSBucketInfo = OSS.getOSSInfo(kBuildEnv, undefined, useOSS);
    const aliClient = new AliOSS({
        accessKeyId: aliOSSBucketInfo.secretId,
        accessKeySecret: aliOSSBucketInfo.secretKey,
        bucket: aliOSSBucketInfo.bucket,
        region: aliOSSBucketInfo.region
    });

    return aliClient.put(...params);
}

class AbcDesktopPluginBuildTool2 extends AbcDesktopPluginBuildTool {
    async updateVersion() {
        let host = "";
        switch (this.buildEnv) {
            case "dev":
                host = "dev.rpc.abczs.cn";
                break;

            case "test":
                host = "test.rpc.abczs.cn";
                break;

            case "prod":
            case "gray":
            case "pre":
                host = "pre.rpc.abczs.cn";
                break;
        }
        const env = this.buildEnv2PluginEnv(this.buildEnv);
        if (env !== -1) {
            await this.savePluginInfo(env, host);
        } else {
            const envs = [{
                label: "正式",
                value: 0
            }, {
                label: "预发布",
                value: 1
            }, {
                label: "灰度",
                value: 2
            }];
            for (const {value: env} of envs) {
                await this.savePluginInfo(env, host);
            }
        }
    }


    async savePluginInfo(env, host) {
        const postData = {
            buildTime: this.buildTime,
            buildTag: this.buildTag,
            name: this.pluginName,
            appId: this.appId,
            description: this.description,
            env, // 0:正式， 1: 预发布, 2:灰度
            forceUpdate: 0,
            hostMaxVersion: this.hostMaxVersion,
            hostMinVersion: this.hostMinVersion,
            md5: this.zipMD5,
            operator: this.operator,
            osVersion: this.osVersion,
            targetPlatform: this.targetPlatform,
            url: this.zipOSSUrl,
            version: this.version
        };

        const url = `http://${host}/rpc/app/plugin/upgrade?${new Date().getTime()}`;
        console.log(`url = ${url}`);
        console.log('版本信息更新开始:');
        const rsp = await axios.post(url,
            postData,
            {
                headers: {
                    'Content-Type': "application/json"
                }
            }).catch(error => {
            throw new Error(JSON.stringify(error.response.data));
        });


        if (rsp.status !== 200) {
            throw new Error(JSON.stringify(rsp.data));
        }

        console.log('版本信息更新完成:', JSON.stringify(rsp.data.data));
    }

    async upload() {
        const uploadDestPath = `/${this.pluginName}/${process.env.BUILD_TAG}.zip`;
        console.log('阿里云开始上传:', this.zipPath, '---->', uploadDestPath, 'MD5:', this.zipMD5);
        const aliResult = await aliPutObject(uploadDestPath, this.zipPath);
        console.log('阿里云上传完成:', aliResult.url);
        this.zipOSSUrl = aliResult.url.replace('-internal', '');
        console.log('zipOSSUrl:', this.zipOSSUrl);
    }
}

const kPluginName = 'abc-vox-desktop-addon';

async function main() {
    const sourceDir = path.resolve("./dist");
    const sourceMap = path.resolve(sourceDir, "index.js.map");
    const zipSourceMap = path.resolve(sourceDir, "index.js.map.zip");
    if (fs.existsSync(sourceMap)) {
        await new Promise((resolve, reject) => {
            const archive = archiver.create('zip', {
                zlib: {
                    level: 9
                },
                password: kSourceMapPassword
            });

            const output = fs.createWriteStream(zipSourceMap);
            archive.pipe(output);
            archive.directory(sourceDir, false, entry => {
                if (entry.name === 'index.js.map.zip' || entry.name === "index.js" || entry.name.startsWith("renderer")) {
                    return false;
                }
                return entry;
            });
            output.on("close", () => {
                console.log(`zip完成${zipSourceMap}:`, zipSourceMap);
                resolve();
            });
            output.on("error", err => {
                console.log(`zip失败${zipSourceMap}:`, err);
                reject(err);
            });
            archive.finalize();
        });


        const uploadDestPath = `/${kPluginName}/${process.env.BUILD_TAG}.source.map.zip`;
        console.log('阿里云开始上传:', zipSourceMap, '---->', uploadDestPath);
        const aliResult = await aliPutObject(uploadDestPath, zipSourceMap);
        const url = aliResult.url.replace('-internal', '');
        console.log('阿里云上传完成:', url);

        // 删除source.map.zip和source.map
        fs.unlinkSync(zipSourceMap);
        fs.unlinkSync(sourceMap);
        //删除dist/src目录
        fs.rmdirSync(path.resolve(sourceDir, "src"), {recursive: true});
    }


    const tools = new AbcDesktopPluginBuildTool2({
        buildTime: process.env.BUILD_TIME || new Date().getTime(),
        pluginName: kPluginName,
        buildTag: process.env.BUILD_TAG,
        buildEnv: kBuildEnv,
        entry: "index.js",
        sourceDir: path.resolve("./dist"),
    });
    await tools.build();
}

main();