const path = require('path');
const {VueLoaderPlugin} = require('vue-loader');
const CopyPlugin = require('copy-webpack-plugin');
const packageJson = require('../package.json');
const webpack = require('webpack');

const common_config = {
    module: {
        rules: [
            {
                test: /\.mjs$/,
                include: /node_modules/,
                type: 'javascript/auto'
            }
        ]
    },
    plugins: [
        new webpack.DefinePlugin({
            'process.env': {
                BUILD_ENV: JSON.stringify(process.env.BUILD_ENV),
                BUILD_TAG: JSON.stringify(process.env.BUILD_TAG),
                BUILD_TIME: JSON.stringify(new Date().toLocaleString())
            },
        }),

        new CopyPlugin([
            {from: 'conf', to: path.resolve(__dirname, '../dist/conf')},
        ]),
    ],
    target: "electron-main",
    output: {
        filename: 'index.js',
        libraryTarget: "commonjs",
        path: path.resolve('./dist'),
    },

    entry: {
        "index": ["./dist/src/index.js"]
    },
}

module.exports = common_config;
