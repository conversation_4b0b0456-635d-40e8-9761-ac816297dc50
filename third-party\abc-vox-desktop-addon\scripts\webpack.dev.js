const path = require('path');
const common_config = require('./webpack.config');

module.exports = [
    Object.assign({}, common_config, {
        mode: 'development',
        watch:true,
        //只有开启监听模式时，watchOptions才有意义
        watchOptions: {
            //默认为空，不监听的文件或者文件夹，支持正册匹配
            ignored:/node_modules/,
            //监听到变化发生后会等到300ms再去执行，默认300ms
            aggregateTimeout:300,
            //判断文件是否发生变化是通过不停轮循系统指定文件有没有变化实现的
            //默认每秒问1000次
            poll:1000
        }
    }),
]
