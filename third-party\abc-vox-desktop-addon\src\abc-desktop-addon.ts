import {GrayEnv} from "./constants";
import {IBundleUpdateProgress} from "./sub-project/front-end-offline-bundle";

export interface INetworkUtils {
    getAsString(url: string): Promise<string>;


    getAsStringAutoCookie(url: string): Promise<string>;

    /**
     *
     * @param url{string}
     * @param headers {any}
     * @returns Promise<string>
     */
    getAsStringWithHeaders(url: string, headers?: any): Promise<string>;

    post(url: string, params: { body: string, headers?: any }): Promise<string>;

    downloadFile(options: {
        url: string,
        filePath: string,
        md5: string,
        onProgress?: (currentBytes: number, totalBytes: number) => void
    }): Promise<void>;
}

export interface IHostLogger {
    info(...params: any[]): void;

    log(...params: any[]): void;

    warn(...params: any[]): void;

    error(...params: any[]): void;
}

export interface IAbcEnvironment {
    grayEnv(): GrayEnv;

    region(): string

    isLaunchFromAbcService: boolean;

    networkConfig?: {
        proxyServer: string;
        proxyBypassList: string
    }

    setRegion(region: string, grayEnv: GrayEnv): void;

    setLoginInfo(chainId: string, clinicId: string, employeeId: string): void;


    /**
     * 是否是开发环境
     */
    get isDev(): boolean;


    get isTest(): boolean;


    get isRelease(): boolean;

    get isDisableGpu(): boolean;

    uuid(): string;

    _lastLoginClinicId?: string;
}

export interface IElectron {
    session: any;
    protocol: any;
    net: any;
    app: any;
    ipcMain: any;
    BrowserWindow: any;
    ipcRenderer: any;
    crashReporter: any;
    globalShortcut: any;
    Menu: any;
    screen: any;
    powerSaveBlocker:any;
    powerMonitor:any;
}

export interface IPathUtils {
    getAppDataRootDir(): string;

    getResourcesDir(): string;

    tmpDir(): string;

    getTmpResourcesDir(): string;

    getFlatResource(): string;

    shebaoAloneResourceDir(): string;

    getProjectPath(): string;

    getAppPluginRootDir(): string;
}


export interface IPluginManager {
    preparePlugin(name: string, checkUpgrade: boolean): Promise<boolean>;

    preparePluginForWindow(window: any/*BrowserWindow*/, name: string, urlParams?: string): Promise<void>;

    getInstalledPluginList(): Promise</*PluginConf*/any[]>

    getPluginUrl(name: string, waitUpdate: boolean): Promise<string>;
}

export interface ISharedPreferences {
    /**
     *
     * @param key {string}
     * @param data {any}
     */
    setObject(key: string, data: any): void;

    /**
     *获取对应的值
     * @param key {string}
     * @return any
     */
    getObject(key: string): any;
}

export interface IAddInitOptions {
    isFirstLaunch: boolean,
    version: string,
    rootDir: string,
    buildConfig: {
        gitCommit: string,
        buildTime: string
    },

    networkUtils: INetworkUtils,
    logger: IHostLogger,
    abcEnvironment: IAbcEnvironment,
    electron: IElectron;
    pathUtils: IPathUtils;
    pluginManager: IPluginManager;

    hostRequire: (module: string) => any;

    sharedPreferences: ISharedPreferences;
}

export interface ISwitchClinicOptions {
    region?: string,
    env?: "pre" | "gray" | "prod",
    chainId?: string,
    clinicId?: string,
    employeeId?: string,

    onBundleUpdateProgress?: IBundleUpdateProgress;
}

export interface AbcDesktopAddon {
    init(options: IAddInitOptions): void;

    onBeforeElectronAppReady(): Promise<void>;

    onAfterElectronAppReady(): Promise<void>;
}
