/**
 * 一些常量定义
 */
const Constants = {
    appName: "AB医生助手",
    abcyunScheme: "abcyun",
    kOfflineBundleAppID: "abcyun-mira-pc"
}

// 0:正式， 1: 预发布, 2:灰度
enum GrayEnv {
    PROD,
    RC,
    GRAY
}

const GrayEnvMap = {
    0: "prod",
    1: "pre",
    2: "gray"
}


const StringToGrayEnvMap = {
    "prod": GrayEnv.PROD,
    "pre": GrayEnv.RC,
    "gray": GrayEnv.GRAY,
    "rc": GrayEnv.RC
}

const kUrlConfig = {
    dev: 'https://dev.abczs.cn',
    test: 'https://test.abczs.cn',
    prod: 'https://www.abcyun.cn',
}


const kProxyRegionHosts = {
    "chongqing": [
        '***********',
        '***********',
        '***********',
    ],
    "wuhan": [
        '************',
        '************'
    ]
}

const kDefaultEntryUrl = "abcyun://www.abcyun.cn/login";
export {Constants, GrayEnv, GrayEnvMap, kUrlConfig,StringToGrayEnvMap, kProxyRegionHosts, kDefaultEntryUrl}
