/**
 * 成都字节星球科技公司
 *
 * Created by <PERSON><PERSON><PERSON> on 2020-04-30
 *
 * @description
 *
 */
import {AnyType} from "./complier";

export class CommonError extends Error {
    constructor(public detailError: AnyType) {
        super(detailError?.message ?? detailError);
    }
}


class NetworkError extends Error {
    code: number;
    detail?: AnyType;

    constructor(code: number, msg: string, detail?: AnyType) {
        super(msg);
        this.code = code;
        this.detail = detail;
    }
}