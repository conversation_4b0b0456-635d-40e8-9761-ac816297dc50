import {abcConfig} from "../../conf/abc-conf";
import {abcHost, hostRequire} from "../../../abc-host";
import {FileUtils} from "./file_utils";

export class AddonPathUtils {
    private static rootPath: string;

    private static _appPluginRootPath: string;

    static setRootPath(rootPath: string) {
        this.rootPath = rootPath;
    }

    public static getRootPath(): string {
        return AddonPathUtils.rootPath;
    }


    static initExtraAppDataPath = false;
    static extraAppDataPath?: string;

    public static getAppDataRootDirPreferExtra(): string {
        const extraAppDataPath = AddonPathUtils.getExtraAppDataPath();
        if (extraAppDataPath)
            return extraAppDataPath;
        return abcHost.pathUtils.getAppDataRootDir();
    }

    public static getExtraAppDataPath(): string {
        if (AddonPathUtils.initExtraAppDataPath) {
            return AddonPathUtils.extraAppDataPath;
        }

        AddonPathUtils.initExtraAppDataPath = true;
        //C盘为默认值不用设置
        if (abcConfig.extraAppDataPath && FileUtils.dirExistsSync(abcConfig.extraAppDataPath, false) && abcConfig.extraAppDataPath.toLowerCase() !== 'c:\\') {
            //提取目录名
            const userDataPath = abcHost.electron.app.getPath("userData");
            const userDataDirName = hostRequire('path').basename(userDataPath);
            AddonPathUtils.extraAppDataPath = AddonPathUtils.extraAppDataPath = hostRequire('path').join(abcConfig.extraAppDataPath, `Bytestream/AppData/${userDataDirName}`);
        }

        return AddonPathUtils.extraAppDataPath;
    }


    public static getAppPluginRootDir(): string {
        if (!AddonPathUtils._appPluginRootPath) {
            AddonPathUtils._appPluginRootPath = hostRequire('path').normalize(abcHost.pathUtils.getAppPluginRootDir());
        }
        return AddonPathUtils._appPluginRootPath;
    }

    /**
     * 获取 addon 自带conf配置文件路径
     */
    public static getAddonConfPath(): string {
        return hostRequire('path').join(AddonPathUtils.getRootPath(), "conf");
    }
}