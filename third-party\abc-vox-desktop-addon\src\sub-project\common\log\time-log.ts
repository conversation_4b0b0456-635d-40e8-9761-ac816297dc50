/**
 * 用于记录耗时的日志
 */
class TimeLog {
    private _tagMap = new Map<string, {
        startTime: number,
        lastTime: number
    }>();

    constructor() {
    }

    /**
     * 开始计时
     */
    time(label: string) {
        if (this._tagMap.has(label)) {
            console.error(`label ${label} already exists`);
        }

        const now = Date.now();
        this._tagMap.set(label, {
            startTime: now,
            lastTime: now
        });
    }

    timeLog(label: string, ...val: any[]): {
        info: string,
        cost: number
    } {
        if (!this._tagMap.has(label)) {
            console.error(`label ${label} not exists`);
        }

        let now = Date.now();
        let start = this._tagMap.get(label)!.lastTime;
        const info = `${label}-${val?.map(it => it.toString())?.join() ?? ''} cost ${now - start}ms`;
        console.log(info);
        this._tagMap.get(label)!.lastTime = now;

        return {
            info: info,
            cost: now - start
        }
    }

    timeEnd(label: string, ...val: any[]): {
        info: string,
        cost: number
    } {
        if (!this._tagMap.has(label)) {
            console.error(`label ${label} not exists`);
        }

        let now = Date.now();
        let start = this._tagMap.get(label)!.startTime;
        const info = `${label}-${val?.map(it => it.toString())?.join() ?? ''}  cost ${now - start}ms`;
        console.log(info);

        this._tagMap.delete(label);

        return {
            info: info,
            cost: now - start
        }
    }
}

const timeLog = new TimeLog();
export {timeLog}