import {CryptoUtils, FileUtils, logger} from "..";
import {abcHost} from "../../../abc-host";


const TAG = "NetworkUtils";

export type Session = any; //electron.Session
interface RequestOptions {
    protocol?: "http:" | "https:";
    hostname?: string;
    port?: number;
    path?: string;
    headers?: Record<string, string>;
    session?: Session;
    method?: "GET" | "POST"
}


class NetworkUtils {
    private static netSessionName = "abc-addon-network-session";
    private static sessionIndex = 0;
    // 解析 Set-Cookie 字段的函数
    static parseCookie(cookieStr: string) {
        const cookie = {name: '', value: '', domain: '', path: '', secure: false, httpOnly: false, expires: undefined}
        const parts = cookieStr.split(';')

        parts.forEach((part, i) => {
            const [key, value] = part.split('=').map(s => s.trim())
            if (i === 0) {
                cookie.name = key
                cookie.value = value
            } else {
                if (key.toLowerCase() === 'domain') {
                    cookie.domain = value
                } else if (key.toLowerCase() === 'path') {
                    cookie.path = value
                } else if (key.toLowerCase() === 'secure') {
                    cookie.secure = true
                } else if (key.toLowerCase() === 'httponly') {
                    cookie.httpOnly = true
                } else if (key.toLowerCase() === 'expires') {
                    cookie.expires = new Date(value).getTime() / 1000
                }
            }
        })

        return cookie
    }

    /**
     *
     * @param url{string}
     * @returns Promise<string>
     */
    static getAsString(url: string): Promise<string> {
        return NetworkUtils.getAsStringWithHeaders(url);
    }

    public static changeSessionNameWhenErrFailed(e: any) {
        // electron NetworkService crash重启后，之前创建的session无法使用(会报ERR_FAILED)，需要换个名称重新创建
        if (e && e.message === 'net::ERR_FAILED') {
            NetworkUtils.netSessionName = `abc-addon-network-session-${NetworkUtils.sessionIndex++}`;
        }
    }


    static async getAsStringAutoCookie(url: string): Promise<string> {
        logger.log(`${TAG}.getAsStringAutoCookie url = ${url}`);
        const electron = abcHost.hostRequire('electron')
        const cookie = (await electron.session.defaultSession.cookies.get({url: url}))?.map((item: any) => `${item.name}=${item.value}`)?.join(';');
        return NetworkUtils.getAsStringWithHeaders(url, {Cookie: cookie});
    }

    /**
     *
     * @param url{string}
     * @param headers {any}
     * @returns Promise<string>
     */
    static getAsStringWithHeaders(url: string, headers?: any): Promise<string> {
        return new Promise((resolve, reject) => {
            const {net} = abcHost.hostRequire('electron');
            const request = net.request(this._createHttpRequest(url));
            if (headers) {
                Object.keys(headers).forEach(key => {
                    request.setHeader(key, headers[key]);
                });
            }

            request.on('response', (res: any) => {
                let result = '';
                res.on('data', (data: any) => {
                    result += data;
                })
                res.on('end', () => {
                    resolve(result);
                })

                res.on('error', (error: any) => {
                    logger.log(`${TAG}.getAsStringWithHeaders failed from rsp url = ${url} error = ${error.message}`);
                    reject(error);
                });
            }).on('error', (e: Error) => {
                logger.log(`${TAG}.getAsStringWithHeaders failed from request url = ${url}, error = ${e.message}, stack = ${e.stack}`);
                NetworkUtils.changeSessionNameWhenErrFailed(e);
                reject(e);
            });
            request.end()
        });
    }


    static post(url: string, params: { body: string, headers?: any }): Promise<string> {
        logger.logIfDebugMode(`${TAG}.post url = ${url}`);
        return new Promise((resolve, reject) => {
            const {body, headers} = params;
            const {net} = abcHost.hostRequire('electron');
            const request = net.request(this._createHttpRequest(url, "POST"));
            if (headers) {
                Object.keys(headers).forEach(key => {
                    request.setHeader(key, headers[key]);
                });
            }

            request.on('response', (res: any) => {
                let result = '';
                res.on('data', (data: any) => {
                    result += data;
                })
                res.on('end', () => {
                    resolve(result);
                });

                res.on('error', (error: any) => {
                    logger.log(`${TAG}.post failed from rsp url = ${url} error = ${error.message}`);
                    reject(error);
                });
            }).on('error', (e: Error) => {
                logger.log(`${TAG}.post failed from request error  url = ${url}, error = ${e.message}, stack = ${e.stack}`);
                NetworkUtils.changeSessionNameWhenErrFailed(e);
                reject(e);
            });

            if (body) {
                request.write(body)
            }

            request.end()
        });
    }

    /**
     *
     * @param options {}}
     *
     * @return Promise<void>
     */
    static downloadFile(options: {
        url: string,
        filePath: string,
        md5: string,
        headers?: any,
        append?: boolean,
        onProgress?: (currentBytes: number, totalBytes?: number) => void
    }) {
        const {url, filePath, md5, headers, append = false, onProgress} = options;
        if (!url) return Promise.reject("url参数缺失");
        if (!filePath) return Promise.reject("filePath参数缺失");

        console.log(`NetworkUtils.downloadFile: ${JSON.stringify(options)}`);
        const fs = abcHost.hostRequire('fs');

        return new Promise(async (resolve, reject) => {
            if (!append && fs.existsSync(filePath)) {
                //检查md5，如果一样就不用重新下载了
                const fileMd5 = await CryptoUtils.fileMd5(filePath);
                if (md5 === fileMd5) {
                    resolve(undefined);
                    return;
                }

                fs.unlinkSync(filePath);
            }

            const file = fs.createWriteStream(options.filePath, {flags: append ? 'a' : 'w'});
            const {net} = abcHost.hostRequire('electron');
            const request = net.request(this._createHttpRequest(url, 'GET', headers));

            let hasRejected = false;
            const rejectWithError = (error: any) => {
                try {
                    file.close();
                } catch (e) {
                }
                try {
                    fs.unlinkSync(filePath, () => {
                    }); // Delete temp file
                } catch (e) {
                }

                if (!hasRejected) {
                    hasRejected = true;
                    reject(error);
                }
            };
            let contentLength = 0;
            let currentBytes = 0;
            request.on('response', (rsp: any) => {
                rsp.pipe(file);
                if (rsp.statusCode != 200 && rsp.statusCode != 206) {
                    logger.logIfDebugMode(`downloadFile failed from rsp url = ${url}, contentLength = ${contentLength}, currentBytes = ${currentBytes}, statusCode = ${rsp.statusCode}`);
                    rejectWithError(new Error(`downloadFile failed from rsp url = ${url}, contentLength = ${contentLength}, currentBytes = ${currentBytes}, statusCode = ${rsp.statusCode}`));
                    return;
                }

                contentLength = parseInt(rsp.headers['content-length']);
                rsp.on('data', (chunk: Buffer) => {
                    currentBytes += chunk.byteLength;
                    onProgress?.(currentBytes, isNaN(contentLength) ? undefined : contentLength);
                });

                rsp.on('error', (error: any) => {
                    logger.log(`downloadFile failed from rsp url = ${url}, contentLength = ${contentLength}, currentBytes = ${currentBytes},error = ${error.message}`);
                    rejectWithError(error);
                });
            });

            request.on('error', (error: any) => {
                logger.log(`downloadFile failed from request url = ${url}, contentLength = ${contentLength}, currentBytes = ${currentBytes}, error = ${error.message}`);
                NetworkUtils.changeSessionNameWhenErrFailed(error);
                rejectWithError(error);
            });

            file.on("finish", () => {
                resolve(undefined);
            });
            file.on('error', (error: any) => {
                logger.log(`downloadFile failed from file url = ${url}, contentLength = ${contentLength}, currentBytes = ${currentBytes}, error = ${error.message}`);
                rejectWithError(error);
            });

            request.end();
        }).then(() => {
            if (md5) {
                return CryptoUtils.fileMd5(filePath).then((rsp: any) => {
                    if (md5 !== rsp)
                        throw `downloadFile md5 not match, current md5 = ${rsp}, expected: ${md5}`;
                })
            }
        });
    }


    /**
     * 分块下载文件，解决部份文件过大后无法一次下载完成
     * @param options
     */
    static async downloadFileWithChunk(options: {
        url: string,
        filePath: string,
        md5: string,
        chunkSize: number,
        onProgress?: (currentBytes: number, totalBytes?: number) => void
    }) {
        const {url, filePath, md5, chunkSize, onProgress} = options;
        if (!url) return Promise.reject("url参数缺失");
        if (!filePath) return Promise.reject("filePath参数缺失");

        const totalBytes = await this.getContentLength(url).catch(error => 0);
        if (totalBytes <= chunkSize) {
            await NetworkUtils.downloadFile(options);
            return;
        }


        const totalChunks = Math.ceil(totalBytes / chunkSize);
        for (let i = 0; i < totalChunks; ++i) {
            const finalBytes = (i + 1) * chunkSize - 1;
            await NetworkUtils.downloadFile({
                ...options,
                md5: null,
                append: i > 0,
                headers: {
                    Range: `bytes=${i * chunkSize}-${i === totalChunks - 1 ? '' : finalBytes}`
                },

                onProgress: (chunkCurrentBytes: number, chunkTotalBytes?: number) => {
                    let currentBytes = i * chunkSize + chunkCurrentBytes;
                    onProgress?.(currentBytes, totalBytes);
                }
            })
        }

        if (md5) {
            return CryptoUtils.fileMd5(filePath).then((rsp: any) => {
                if (md5 !== rsp)
                    throw `downloadFileWithChunk md5 not match, current md5 = ${rsp}, expected: ${md5}`;
            })
        }
    }

    static getContentLength(url: string): Promise<number> {
        return new Promise((resolve, reject) => {
            const {net} = abcHost.hostRequire('electron');
            const request = net.request(this._createHttpRequest(url));
            request.on('response', (rsp: any) => {
                resolve(parseInt(rsp.headers['content-length']));
                request.abort();
            });
            request.on('error', (e: Error) => {
                reject(e);
            });
            request.end();
        });
    }

    /**
     * 更新系统host文件，指定host ip映射刘方敏
     * @param hosts
     */
    public static updateHostFile(hosts: Array<{ host: string, ip: string, comment: string }>) {
        // const hosts: Array<{ host: string, ip: string }> = JSON.parse(jsonStr).hosts;
        const hostFile = `C:\\Windows\\System32\\drivers\\etc\\hosts`;
        const fs = abcHost.hostRequire('fs')
        const iconvLite = abcHost.hostRequire('iconv-lite');

        let content = "";
        //读取原始host文件
        if (FileUtils.fileExist(hostFile)) {
            content = iconvLite.decode(fs.readFileSync(hostFile), "GBK") as string;
        }
        logger.log(`更新host配置, 原始内容 = ${content}`);

        //需要设置host映射
        const needSetHostsMap = new Map(hosts.map(item => [item.host, item.ip]));

        const abcComment = "#ABC诊所管家";
        const newLine = "\r\n";
        const existHosts = new Map<string, string>(); //host->ip
        const abcAddedHosts = new Map<string, string>();
        let finalHostContent = content.split(newLine).filter(item => {
            //删除我们自己添加的注释
            if (item.startsWith(abcComment)) return false;

            //其它注释保留
            if (item.startsWith("#")) return true;

            const ipHostPair = item.split(" ").filter(token => token.length > 0);
            const ip = ipHostPair[0];
            const host = ipHostPair[1];
            existHosts.set(host, ip);

            //过滤掉我们添加的项目
            if (item.indexOf(abcComment) > 0) {
                abcAddedHosts.set(host, ip);
                return false;
            }

            //保留不是将要设置的项目
            return !(ipHostPair.length >= 2 && needSetHostsMap.has(ipHostPair[1]));
        }).join(newLine);


        let needUpdate = false;
        //检测已经存在的host映射是否包含了所有要配置项的
        for (const host of needSetHostsMap.keys()) {
            const newValue = needSetHostsMap.get(host);
            const oldValue = existHosts.get(host);
            if (newValue != oldValue) {
                needUpdate = true;
                break;
            }
        }

        if (!needUpdate) {
            //检查是否是有老的配置项要删除
            for (const host of abcAddedHosts.keys()) {
                if (!needSetHostsMap.has(host)) {
                    needUpdate = true;
                    break;
                }
            }
        }

        logger.log(`更新host配置,是否更新检测：${needUpdate}`);
        if (!needUpdate) {
            return;
        }

        finalHostContent += [
            newLine,
            abcComment,

            ...hosts.filter(item => item.ip).map(item => `${item.ip} ${item.host} ${item.comment ? `#${item.comment}` : '#ABC配置'}`)
        ].join(newLine);

        const finalHostContentBuffer = iconvLite.encode(finalHostContent, "GBK");
        fs.writeFileSync(hostFile, finalHostContentBuffer);

        logger.log(`finalHostContent = ${finalHostContent}`);
    }

    public static async generateRandomPort(start = 10000, end = 65535) {
        //最大尝试100次
        let tryCount = 0;
        while(tryCount++ < 100) {
            const port = Math.floor(Math.random() * (end - start) + start);
            if(await NetworkUtils.isVaildPort(port)) {
                return port;
            }
        }

        return -1;
    }


    /**
     * 判断一个端口是否可用
     * @param port 
     */
    public static isVaildPort(port: number) :Promise<boolean> {
        //尝试一次Bind操作
        const net = abcHost.hostRequire('net');
        const server = net.createServer();
        return new Promise((resolve, reject) => {
            server.listen(port, '127.0.0.1', () => {
                server.close(() => {
                    resolve(true);
                });
            }).on('error', (err: any) => {
                if (err.code === 'EADDRINUSE') {
                    resolve(false);
                } else {
                    reject(err);
                }
            });
        });
    }

    private static _createHttpRequest(url: string, method?: "GET" | "POST", headers?: any): RequestOptions {
        return {
            ...this._createRequestOptions(url, headers),
            session: this.getNetSession(),
            method: method ? method : "GET"
        }
    }

    public static getNetSession(): Session {
        const {session} = abcHost.hostRequire('electron');
        return session.fromPartition(NetworkUtils.netSessionName, {
            cache: false
        });
    }

    private static _configureRequestUrl(url: URL, options: RequestOptions): void {
        options.protocol = url.protocol as any;
        options.hostname = url.hostname;

        if (url.port) {
            options.port = parseInt(url.port);
        }

        options.path = url.pathname + url.search;
    }

    private static _createRequestOptions(url: string, headers?: any) {
        const result: RequestOptions = {};
        result.headers = headers;
        this._configureRequestUrl(new URL(url), result);
        return result;
    }
}

export {NetworkUtils}
