class Version {
    major: number = 0;
    minor: number = 0;
    revision: number = 0;
    build: number = 0;

    /**
     * 5.1.0.xxx
     * @param version {string}
     */
    constructor(version: string) {
        const tokens = version.split(".");
        if (tokens.length >= 1) {
            this.major = parseInt(tokens[0]);
        }
        if (tokens.length >= 2) {
            this.minor = parseInt(tokens[1]);
        }

        if (tokens.length >= 3) {
            this.revision = parseInt(tokens[2]);
        }

        if (tokens.length >= 4) {
            this.build = parseInt(tokens[3]);
        }
    }

    /**
     * @param version {Version | string}
     * @return {number}
     */
    compareTo(version: string | Version): number {
        if (typeof version === "string") {
            version = new Version(version);
        }

        if (this.major !== version.major) {
            return this.major - version.major;
        }

        if (this.minor !== version.minor) {
            return this.minor - version.minor;
        }

        if (this.revision !== version.revision) {
            return this.revision - version.revision;
        }

        return this.build - version.build;
    }
}


export {Version}
