import { UiUtils } from "./ui-utils";
import utils from "../common/utils";
import path from "path";
import { AddonPathUtils } from "../common/file/addon-path-utils";
import { abcHost } from "../../abc-host";

const TAG = "Alert";

interface AlertOptions {
    type: "info" | "error" | "warn" | "success";
    title: string;
    content: string;
}

export async function alert(mainWindow: any/*BrowserWindow*/, options: AlertOptions) {
    return new Promise((resolve) => {
        const ipcMain = abcHost.electron.ipcMain;
        const alertWindow = utils.createBrowserWindow({
            ...UiUtils.createFillParentParams(mainWindow),
            show: true,
            resizable: false,
            movable: false,
        });

        if (mainWindow.isMaximized()) {
            alertWindow.maximize();
        }

        const sendId = `${TAG}:_alert${alertWindow.id}`;

        alertWindow.loadURL(`file://${path.resolve(AddonPathUtils.getRootPath(), `renderer/alert.html?title=${options.title}&content=${options.content}&sendId=${sendId}`)}`).then();

        ipcMain.once(sendId, (_event: any/*Event*/, arg: any) => {
             if(arg.type === 'close') {
                 alertWindow.close();
                 resolve(true);
             }
        });
    });
}