export interface IPluginConf {
    name: string;
    version: string;
    updateTime: string;
    md5: string;
    asarMd5:string;
    buildTime: string;
}


export interface IOfflineBundle {
    name: string;
    global?: boolean;
}

export interface ILocalBundleUpdateInfo {
    name:string;
    oldVersion:string;
    newVersion:string;
}

// 前端js api调用检查本地是否有准备好的bundle需要更新
export interface IUpdateLocalOffBundleResult {
    updateBundles?: ILocalBundleUpdateInfo[];
    hasUpdate: boolean //是否有更新
}

export interface IOfflineBundleWithUpdateInfo extends IOfflineBundle {
    version: string;
    updateTime: string;
    buildTime: string;
    rootDir: string;
}


export interface OfflineBundleUpdateMsg {
    appId: string;
    name: string;
    env: number;
    region: string;
}

export interface IOfflineBundleInfos {
    grayEnv: string;
    region: string;
    list: IOfflineBundleWithUpdateInfo[];
}