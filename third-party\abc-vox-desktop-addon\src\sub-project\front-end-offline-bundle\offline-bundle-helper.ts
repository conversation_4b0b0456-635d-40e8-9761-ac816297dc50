import {IOfflineBundle, IPluginConf} from "./offline-bundle-common-data";
import path from "path";
import {AddonPathUtils, FileUtils, logger} from "../common";
import {GrayEnv, GrayEnvMap} from "../../constants";
import {hostRequire} from "../../abc-host";
import {abcConfig} from "../conf/abc-conf";

/**
 * 辅助工具
 */
export class OfflineBundleHelper {
    // bundles
    private _bundles: IOfflineBundle[] = [];

    // 全局插件
    private _globalPlugins: Set<string> = new Set();


    //当前正使用的插件信息
    private _currentUsedPluginConfMap = new Map<string, IPluginConf>();


    /**
     * 设置bundles
     */
    setBundles(bundles: IOfflineBundle[]) {
        this._bundles = bundles;
        this._globalPlugins.clear();
        for (const bundle of bundles) {
            if (bundle.global) {
                this._globalPlugins.add(bundle.name);
            }
        }
    }

    /**
     * 获取bundles
     */
    public bundles() {
        return this._bundles;
    }

    public globalPlugins() {
        return this._globalPlugins;
    }

    public frontEndOfflineBundleGlobalDir(): string {
        return this.frontEndOfflineBundleDir() + "/global";
    }

    public frontEndOfflineBundleDir(createIfNotExist: boolean = true) {
        const dir = path.join(AddonPathUtils.getAppDataRootDirPreferExtra() ,'front-end-offline-bundle-asar',  abcConfig.env);
        FileUtils.dirExistsSync(dir, createIfNotExist);
        return dir;
    }

    private pluginRootDir(env: GrayEnv, region: string, global: boolean = false) {
        //如果region为空，调整为default-region
        if (!region) {
            region = 'region1';
        }

        const envStr = GrayEnvMap[env];
        let dir = '';
        if (global) {
            dir = this.frontEndOfflineBundleGlobalDir();
        } else {
            dir = this.frontEndOfflineBundleDir() + "/" + region + "/" + envStr + "/";
        }

        FileUtils.dirExistsSync(dir, false);

        return dir;
    }

    /**
     * 获取插件的根目录
     */
    public pluginDir(pluginName: string, env: GrayEnv, region: string) {
        const global = this._globalPlugins.has(pluginName);
        return hostRequire('path').join(this.pluginRootDir(env, region, global), pluginName);
    }


    /**
     * 获取插件的根目录
     */
    public pluginConfDir(pluginName: string, env: GrayEnv, region: string) {
        const global = this._globalPlugins.has(pluginName);
        let dir = this.pluginRootDir(env, region, global) + "/__conf__/" + pluginName;
        FileUtils.dirExistsSync(dir, true);
        return dir;
    }

    public pluginConfFile(pluginName: string, env: GrayEnv, region: string) {
        return path.join(this.pluginConfDir(pluginName, env, region), "plugin.json");
    }


    public pluginAsarFile(name: string, region:string, grayEnv: GrayEnv) {
        const pluginDir = this.pluginDir(name, grayEnv, region);
        const conf = this.inUseLocalBundleConf(name, grayEnv, region);
        return hostRequire('path').join(pluginDir, `${conf.name}-${conf.version}.asar`);
    }

    public fileWithinPlugin(name: string, region:string, grayEnv: GrayEnv, pathname: string, filePrefix?: string) {
        const pluginDir = this.pluginAsarFile(name, region, grayEnv);
        if (filePrefix) {
            return hostRequire('path').join(pluginDir, filePrefix, pathname);
        }
        else {
            return hostRequire('path').join(pluginDir, pathname);
        }
    }

    public inUseLocalBundleConf(plugin: string, env: GrayEnv, region: string):IPluginConf{
        let conf = this._currentUsedPluginConfMap.get(this._pluginKey(plugin, region, env));
        if (conf) {
            return conf;
        }

        const confFile = this.pluginConfFile(plugin, env, region);
        conf = {name: plugin} as IPluginConf;
        if (FileUtils.fileExist(confFile)) {
            try {
                conf = JSON.parse(FileUtils.readFileAsString(confFile)) as IPluginConf;
                this._currentUsedPluginConfMap.set(this._pluginKey(plugin, region, env), conf);
            } catch (e) {
                logger.logToServer(`parse ${confFile} failed for ${e}`);
            }
        }

        return conf;
    }

    public resetInUseLocalBundleConf(plugin: string, env: GrayEnv, region: string) {
        this._currentUsedPluginConfMap.delete(this._pluginKey(plugin, region, env));
    }

    /**
     * 获取本地插件配置信息
     *
     */
    public localBundleConf(plugin: string, grayEnv: GrayEnv, region: string): IPluginConf {
        const confFile = this.pluginConfFile(plugin, grayEnv, region);
        const newConfFile = confFile + "_new";
        let conf: IPluginConf = {name: plugin} as IPluginConf;

        if (FileUtils.fileExist(newConfFile)) {
            try {
                const newConf = JSON.parse(FileUtils.readFileAsString(newConfFile)) as IPluginConf;
                const newPluginDir = this.pluginDir(plugin, grayEnv, region) + "_new";
                const newAsarFile =  hostRequire('path').join(newPluginDir, `${newConf.name}-${newConf.version}.asar`);
                const isExist = FileUtils.fileExist(newAsarFile);
                if (!isExist) {
                    logger.error(`newAsarFile:${newAsarFile}不存在`);
                    return conf;
                }
                return newConf;
            } catch (e) {
            }
        }

        if (FileUtils.fileExist(confFile)) {
            try {
                conf = JSON.parse(FileUtils.readFileAsString(confFile)) as IPluginConf;
                const pluginDir = this.pluginDir(plugin, grayEnv, region);
                const asarFile =  hostRequire('path').join(pluginDir, `${conf.name}-${conf.version}.asar`);
                const isExist = FileUtils.fileExist(asarFile);
                if (!isExist) {
                    logger.error(`asarFile:${asarFile}不存在`);
                    return {name: plugin} as IPluginConf;
                }
            } catch (e) {
                logger.logToServer(`parse ${confFile} failed for ${e}`);
            }
        }

        return conf;
    }

    public _pluginKey(plugin: string, region:string, grayEnv: GrayEnv): string {
        if (this._globalPlugins.has(plugin)) {
            return `${plugin}-${region}`;
        }
        return `${plugin}-${region}-${grayEnv}`;
    }

}
