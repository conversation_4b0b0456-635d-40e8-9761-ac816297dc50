import {logger, AnyType, NetworkUtils} from "../common";
import * as path from "path";
import * as fs from "fs";
import {abcHost} from "../../abc-host";

const kChannelName = "call_download_manager";
const kChannelCallbackName = "call_download_manager_callback";

interface DownloadOptions {
    url: string,
    file: string,
    md5?: string,

    onProgress?: (id: number, currentBytes: number, totalBytes?: number) => void;
    onComplete?: (id: number, fullPath: string, error?: AnyType) => void;
}

interface DownloadWithSplitAbleOptions extends DownloadOptions {
    chunkSize?: number
}


interface EventBase {
    type: "progress" | "complete"
}

interface ProgressEvent extends EventBase {
    currentBytes: number;
    totalBytes?: number
}


interface CompleteEvent extends EventBase {
    error?: AnyType;
    fullPath?: string;
}

function downloaderKey(url: string, file: string): string {
    return `${url}-${file}`;
}

class Downloader {
    constructor(public options: DownloadWithSplitAbleOptions, public readonly id: number) {

    }

    start() {
        const {url, file, md5, chunkSize} = this.options;
        const fullFilePath = path.resolve(abcHost.electron.app.getPath('userData'), file);
        const filePath = path.dirname(fullFilePath);
        if (!fs.existsSync(filePath)) {
            fs.mkdirSync(filePath, {recursive: true});
        }

        let task: Promise<void>;
        if (chunkSize > 0) {
            task = NetworkUtils.downloadFileWithChunk({
                url: url, filePath: fullFilePath, md5: md5!,
                chunkSize: chunkSize,
                onProgress: (currentBytes: number, totalBytes?: number) => {
                    this.options.onProgress?.(this.id, currentBytes, totalBytes);
                }
            })
        } else {
            task = NetworkUtils.downloadFile({
                url: url,
                filePath: fullFilePath,
                md5: md5!,
                onProgress: (currentBytes: number, totalBytes?: number) => {
                    this.options.onProgress?.(this.id, currentBytes, totalBytes);
                }
            })
        }

        task.then(ignored => {
            this.options.onComplete?.(this.id, fullFilePath);
        }).catch(error => {
            this.options.onComplete?.(this.id, fullFilePath, error);
        });
    }
}


class DownloadManagerImpl {
    private idGenerator: number = 0;

    private _downloaders: Downloader[] = [];

    constructor() {
    }

    downloadFile(options: DownloadOptions): number {
        console.log(`DownloadManagerImpl options = ${JSON.stringify(options)}`);
        const {url, file} = options;
        const key = downloaderKey(url, file);
        const existDownloader = this._downloaders.find(item => downloaderKey(item.options.url, item.options.file) == key);
        if (existDownloader) return existDownloader.id;

        const id = this.idGenerator++;
        const downloader = new Downloader({
            ...options, onProgress: this._onProgress.bind(this),
            onComplete: (id: number, fullPath: string, error?: AnyType) => {
                if (!error) {
                    this._onComplete(id, fullPath, error);
                } else {
                    const index = this._downloaders.findIndex(item => item.id == id);
                    index >= 0 && this._downloaders.splice(index, 1);

                    //每一次下载失败了，尝试分片下载一次
                    const secondTry = new Downloader({
                        ...options,
                        chunkSize: 5 * 1024 * 1024,
                        onProgress: this._onProgress.bind(this),
                        onComplete: this._onComplete.bind(this)
                    }, id);
                    this._downloaders.push(secondTry);
                    secondTry.start();
                }
            },
        }, id);
        this._downloaders.push(downloader);
        downloader.start();

        return id;
    }

    private _onProgress(id: number, currentBytes: number, totalBytes?: number): void {
        const event: ProgressEvent = {
            type: "progress",
            currentBytes: currentBytes, totalBytes: totalBytes
        };
        this._sendEvent(id, event);
    }

    private _onComplete(id: number, fullPath: string, error?: AnyType): void {
        const index = this._downloaders.findIndex(item => item.id == id);
        const event: CompleteEvent = {
            type: "complete",
            fullPath: fullPath,
            error: error
        };
        index >= 0 && this._downloaders.splice(index, 1);

        this._sendEvent(id, event);
    }

    private _sendEvent(id: number, event: EventBase) {
        abcHost.electron.BrowserWindow.getAllWindows().forEach(function (wnd: any) {
            wnd.webContents.send(kChannelCallbackName, id, event);
            const views = wnd.getBrowserViews() ?? [];
            for (const view of views) {
                view.webContents.send(kChannelCallbackName, id, event);
            }
        });

    }
}


export class DownloadManagerBridge {
    downloadManager: DownloadManagerImpl;

    constructor() {
        const promiseIpc = abcHost.hostRequire('electron-promise-ipc');
        this.downloadManager = new DownloadManagerImpl();
        const self = this;
        ///监听成都银海社保调用接口
        promiseIpc.on(kChannelName, async (action: string, ...param: any) => {
            try {
                let realParams = param.splice(0, param.length - 1);
                let fun = (self.downloadManager as any)[action].bind(self.downloadManager);
                let ret = await fun(...realParams);

                return {
                    success: true,
                    ret: ret
                };
            } catch (e: any) {
                const type = (process as any).type;
                logger.log(type + kChannelName + ',  faield for ', action, "param", param, "error =", e);
                let message = e.message;
                if (!message)
                    message = '';
                message += e.stack;

                return {
                    success: false,
                    ret: message
                };
            }
        });
    }
}


