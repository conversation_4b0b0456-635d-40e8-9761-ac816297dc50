import {hostRequire} from "../../abc-host";
import {ProcessUtils} from "./process-utils";
import {timeoutAction} from "../common";

export class OsUtils {
    /**
     * 获取操磁盘信息，内存信息，CPU信息
     */
    public async getOsInfo() {
        let driverCInfo = await timeoutAction(async () => {
            return await ProcessUtils.runCmd(`fsutil volume diskfree c:`);
        }, 2000).catch(() => null);
        return {
            disk: driverCInfo?.stdout,
            memory: this.getMemory(),
            cpu: this.getCpu()
        };
    }

    /**
     * 获取磁盘信息
     */
    public getDisk(): any {
        const os = hostRequire('os');
        return os.totalmem();
    }

    /**
     * 获取内存信息
     */
    public getMemory(): any {
        const os = hostRequire('os');
        return {
            total: os.totalmem(),
            free: os.freemem()
        }
    }

    /**
     * 获取CPU信息
     */
    public getCpu(): any {
        const os = hostRequire('os');
        const cpus = os.cpus();
        return {
            model: cpus[0].model,
            speed: cpus[0].speed,
            cores: cpus.length
        }
    }
}