import {LatestPluginInfo, PluginConf, PluginInfoWithLocalEntryUrl} from "./plugin-conf";
import {abcHost, hostRequire} from "../../abc-host";
import {AddonPathUtils, Completer, delay, FileUtils, ignore, logger, NetworkUtils} from "../common";
import {abcConfig} from "../conf/abc-conf";
import {GrayEnv, kUrlConfig, StringToGrayEnvMap} from "../../constants";


const kTAG = 'PluginManager';

// 插件目录结构
// abc_plugins
// ├── prod
// │   ├── abc-app-tab
// │   │   ├── __conf__
// │   │   │   ├── gray
// │   │   │   │   ├── abc-app-tab-gray.zip
// │   │   ├── gray
// │   │   │   ├── conf.json


export class PluginNames {
    public static KAbcClinicDesktopAddon = 'abc-vox-desktop-addon';
}

interface IPluginUpdateProgressInfo {
    completer: Completer<boolean>;
}

/**
 * 插件管理，用于下载更新插件资源
 */
class PluginManager {
    private _onlineLatestPluginInfo = new Map<GrayEnv, LatestPluginInfo[]>()
    private _embedPluginsConfig?: {
        plugins: {
            name: string,
            buildTime: number
        }[]
    };
    private _updatingBundles = new Map<string, IPluginUpdateProgressInfo>();

    private _nativeLibMap = new Map<string, any>();


    public constructor() {
        try {
            const confStr = hostRequire("fs").readFileSync(`${this._embedPluginRootDir()}/conf.json`).toString();
            this._embedPluginsConfig = JSON.parse(confStr);
        } catch (e) {
        }
    }

    /**
     * 获取当前已经安装的插件列表
     */
    public async getInstalledPluginList(): Promise<PluginConf[]> {
        const grayEnv = abcHost.abcEnvironment.grayEnv();
        const plugins = [
            PluginNames.KAbcClinicDesktopAddon,
        ].map(it => this.getPluginInfo(it, grayEnv));

        return plugins.filter(item => !!item);
    }

    public getPluginEntryUrl(name: string, grayEnv: GrayEnv): string | null {
        const conf = this.getPluginInfo(name, grayEnv);
        return conf?.localEntry;
    }

    public getPluginInfo(name: string, grayEnv: GrayEnv): PluginInfoWithLocalEntryUrl | null {
        const rootDir = this._getPluginRootDir(name, grayEnv);
        const confFile = rootDir + "/conf.json";
        logger.log(`getPluginInfo confFile = ${confFile}`)
        if (!FileUtils.fileExist(confFile))
            return null;

        let conf: PluginConf;
        try {
            const jsonContent = FileUtils.readFileAsString(confFile);
            conf = JSON.parse(jsonContent);
        } catch (e) {
            ignore(e);
            return null;
        }

        return {
            ...conf,
            localEntry: `${rootDir}/${conf.entry}`
        }
    }

    /**
     * 准备插件
     * @param name 插件名称
     * @param checkUpgrade 检查并升级
     * @return boolean true表示有更新发生
     */
    public async preparePlugin(name: string, checkUpgrade = true, grayEnv: GrayEnv): Promise<boolean> {
        logger.log(kTAG, `准备插件 ${name}， 检查更新:${checkUpgrade}`);

        let config = this.getPluginInfo(name, grayEnv);
        let embedConf = this._embedPluginsConfig?.plugins?.find(it => it.name === name);
        //插件不存在，从本地复制一份过去
        if (!config || this._buildTimeCompare(config.buildTime, embedConf?.buildTime) < 0 || !FileUtils.fileExist(config.localEntry)) {
            this._copyFromEmbed(name, grayEnv);
        }

        //判断是否要更新
        const pluginUpdateEnable = abcConfig.pluginAutoUpdate ?? true;
        if (!pluginUpdateEnable || !checkUpgrade) return false;

        //重新获取配置
        config = this.getPluginInfo(name, grayEnv);
        const pluginInfoList = await this._refreshLatestPluginList(grayEnv);
        logger.log(kTAG, `插件${name}当前配置： ${JSON.stringify(config)}`);
        const latestPluginInfo = pluginInfoList.find(item => item.name === name);

        logger.log(kTAG, `插件${name}最新配置 = ${JSON.stringify(latestPluginInfo)}`)
        //本地版本与线上版本不一致
        if (latestPluginInfo && this._buildTimeCompare(config?.buildTime, latestPluginInfo.buildTime) < 0) {
            return await this._updatePlugin(latestPluginInfo, grayEnv);
        }

        //没有更新
        return false;
    }

    private _buildTimeCompare(buildTime1?: string | number, buildTime2?: string | number) {
        let buildTime1Num = buildTime1 ? Number(buildTime1) : 0;
        let buildTime2Num = buildTime2 ? Number(buildTime2) : 0;

        return buildTime1Num - buildTime2Num;
    }

    public async preparePluginForWindow(window: any/*BrowserWindow*/, name: string, urlParams?: string): Promise<void> {
        const grayEnv = abcHost.abcEnvironment.grayEnv();
        await this.preparePlugin(name, false, grayEnv);
        let entryUrl = await this.getPluginEntryUrl(name, grayEnv);
        if (urlParams) {
            if (entryUrl.indexOf("?") >= 0) {
                entryUrl = entryUrl + "&" + urlParams;
            } else {
                entryUrl = entryUrl + "?" + urlParams;
            }
        }

        if (entryUrl) {
            const url = `file:///${entryUrl}`;
            await window.loadURL(url).then().catch((error) => logger.logToServer(kTAG, `${name} 加载url失败 ${url}, error = ${error}`));
        }

        //触发更新
        const update = await this.preparePlugin(name, true, grayEnv);
        if (update) {
            const entryUrl = await this.getPluginEntryUrl(name, grayEnv);
            const url = `file:///${entryUrl}`;
            logger.log(kTAG, `插件更新后，重新加载:${url}`);
            window.loadURL(url).then().catch((error) => logger.logToServer(kTAG, `${name} 加载url失败 ${url}, error=${error}`));
        }
    }


    /**
     * 获取插件对应的地址
     * @param name 插件名称
     * @param waitUpdate 是否等更新完成
     */
    public async getPluginUrl(name: string, waitUpdate = true): Promise<string> {
        const grayEnv = abcHost.abcEnvironment.grayEnv();
        await this.preparePlugin(name, false, grayEnv);
        let entryUrl = await this.getPluginEntryUrl(name, grayEnv);

        const prepareResult = this.preparePlugin(name, true, grayEnv);
        ignore(prepareResult);
        if (!entryUrl && !waitUpdate) {
            return entryUrl;
        }

        return this.getPluginEntryUrl(name, grayEnv);
    }


    private _getPluginRootConfDir(name: string, grayEnv: GrayEnv): string {
        let subEnv = "prod";
        switch (grayEnv) {
            case GrayEnv.RC:
                subEnv = "rc";
                break;
            case GrayEnv.GRAY:
                subEnv = "gray";
                break;
            default:
                break;
        }

        return `${AddonPathUtils.getAppPluginRootDir()}/${abcConfig.env}/${name}/__conf__/${subEnv}`;
    }

    private _getPluginConfFile(name: string, grayEnv: GrayEnv): string {
        let subEnv = "prod";
        switch (grayEnv) {
            case GrayEnv.RC:
                subEnv = "rc";
                break;
            case GrayEnv.GRAY:
                subEnv = "gray";
                break;
            default:
                break;
        }

        return `${this._getPluginRootConfDir(name, grayEnv)}/conf.json`;
    }

    private _getPluginRootDir(name: string, grayEnv: GrayEnv): string {
        let subEnv = "prod";
        switch (grayEnv) {
            case GrayEnv.RC:
                subEnv = "rc";
                break;
            case GrayEnv.GRAY:
                subEnv = "gray";
                break;
            default:
                break;
        }

        return `${abcHost.pathUtils.getAppPluginRootDir()}/${abcConfig.env}/${name}/${subEnv}`;
    }


    /**
     * 获取最新插件列表
     * @private
     */
    private async _refreshLatestPluginList(grayEnv: GrayEnv): Promise<LatestPluginInfo[]> {
        const urlPrefix = kUrlConfig[abcConfig.env];
        //0:正式， 1: 预发布, 2:灰度

        let list = this._onlineLatestPluginInfo.get(grayEnv);
        if (!list) {
            const latestApiUrl = `${urlPrefix}/api/v2/app/plugin/latest?appId=abcyun-mira-pc&version=${abcHost.version}&platform=3&env=${grayEnv}`
            const latestInfo = await NetworkUtils.getAsString(latestApiUrl);
            logger.info(kTAG, `下载插件列表:${latestApiUrl}, 最新插件配置 = ${latestInfo}`);
            list = JSON.parse(latestInfo).data?.list ?? [];
            this._onlineLatestPluginInfo.set(grayEnv, list);
        }

        return list;
    }


    private _embedPluginRootDir(): string {
        return `${abcHost.pathUtils.getResourcesDir()}/abc-plugins`;
    }

    /**
     * 从内置包里复制插件到指定位置
     * @param name
     * @private
     */
    private _copyFromEmbed(name: string, grayEnv: GrayEnv) {
        const srcZip = `${this._embedPluginRootDir()}/${name}.zip`;
        if (!FileUtils.fileExist(srcZip)) {
            logger.error(kTAG, `查找内置版本插件${name} 失败, srczip = ${srcZip}`);
            return;
        }
        logger.log(kTAG, `首次加载 ${name}，从内置包复制, srcZip = ${srcZip}`);
        const targetDir = this._getPluginRootDir(name, grayEnv);
        FileUtils.dirExistsSync(targetDir, true);
        logger.log(kTAG, `复制 ${name}完成，开始解压, srcZip = ${srcZip}, targetDir = ${targetDir}`);

        const AdmZip = require('adm-zip');
        const zip = new AdmZip(srcZip);
        zip.extractAllTo(targetDir, true);

        const latestConfig = this.getPluginInfo(name, grayEnv);
        if (latestConfig?.buildTime) {
            const target = this._getPluginRootConfDir(name, grayEnv);
            const targetFile = `${target}/${latestConfig.buildTime}-${name}.zip`;
            FileUtils.copyDirSyncIgnoreError(srcZip, targetFile);
        }

        logger.log(kTAG, `解压完成 ${name}`);
    }

    /**
     * 下载更新插件
     * @param plugin 最新的插件配置
     * @private 更新成功返回true,否则返回false
     */
    private async _updatePlugin(plugin: LatestPluginInfo, grayEnv: GrayEnv): Promise<boolean> {
        const puginName = plugin.name;
        logger.log(kTAG, `更新插件 name ${puginName}, info = ${JSON.stringify(plugin)}`);

        const updateLockKey = this._pluginUpdateKey(plugin.name, grayEnv);
        let updatingInfo: IPluginUpdateProgressInfo | undefined = this._updatingBundles.get(updateLockKey);
        if (updatingInfo) {
            logger.log(kTAG, `插件 ${plugin} granEnv=${grayEnv} 正在更新中，加入等待列表`);
            return updatingInfo.completer.promise;
        }


        // 新触发更新
        updatingInfo = {
            completer: new Completer<boolean>(),
        };

        this._updatingBundles.set(updateLockKey, updatingInfo);

        //下载最新插件
        const tmpFile = hostRequire('path').join(this._getPluginRootConfDir(puginName, grayEnv), `${puginName}-${plugin.buildTime}-tmp.zip`);
        const targetFile = hostRequire('path').join(this._getPluginRootConfDir(puginName, grayEnv), `${puginName}-${plugin.buildTime}.zip`);
        const dirname = hostRequire('path').dirname(tmpFile);
        FileUtils.dirExistsSync(dirname, true);

        await NetworkUtils.downloadFile({
            url: plugin.url,
            md5: plugin.md5,
            filePath: tmpFile
        });

        //下载后解压
        const rootDir = await this._getPluginRootDir(plugin.name, grayEnv);
        await FileUtils.deleteDir(rootDir);

        let zipFile = tmpFile;
        try {
            hostRequire('fs').renameSync(tmpFile, targetFile);
            zipFile = targetFile;

            //删除目录下的历史zip文件
            const parentDir = hostRequire('path').dirname(targetFile)
            const targetFileName = hostRequire('path').basename(targetFile)
            const files = await FileUtils.lsDir(parentDir);
            for (let file of files) {
                if (file.endsWith('.zip') && file !== targetFileName) {
                    FileUtils.deleteFileSync(hostRequire('path').join(parentDir, file));
                }
            }
        } catch (e) {
            ignore(e);
        }

        logger.log(kTAG, `插件下载完成开始解压 name ${plugin.name}, info = ${JSON.stringify(plugin)}`);
        const AdmZip = require('adm-zip');
        const zip = new AdmZip(zipFile);
        zip.extractAllTo(rootDir, true);

        try {
            const confFile = this._getPluginConfFile(puginName, grayEnv);
            await FileUtils.writeFileAsString(confFile, JSON.stringify(plugin));
        } catch (e) {
            ignore(e);
        }

        //更新完成后，做一次校验
        const latestConfig = await this.getPluginInfo(plugin.name, grayEnv);
        if (latestConfig.buildTime?.toString() !== plugin.buildTime?.toString()) {
            logger.error(kTAG, `插件更新后检查失败, 构建时间（buildTime)不一致，配置(${plugin.buildTime}, 配置文件中为:${latestConfig.buildTime}`);
        }

        this._updatingBundles.delete(updateLockKey);
        updatingInfo.completer.resolve(true);
        return true;
    }

    private _pluginUpdateKey(plugin: string, grayEnv: GrayEnv) {
        return `${plugin}_${grayEnv}`;
    }

    /**
     * 如果指定文件不存在或者内容不合法，重新解压
     * @param file
     */
    async restoreFileIfNeed(file: string) {
        file = hostRequire('path').normalize(file);
        //如果是插件目录下的文件，且文件内容不合法，重新解压
        const pluginRootDir = AddonPathUtils.getAppPluginRootDir();
        if (!file.startsWith(pluginRootDir) || FileUtils.checkFileContentValid(file)) {
            return;
        }

        //file 格式:C:\Users\<USER>\AppData\Roaming\ABCClinicDesktop\abc_plugins\prod\abc-app-tab\gray\xxx
        //获取环境(prod), 插件名称(abc-app-tab), 灰度环境(gray)
        const subPath = file.substring(pluginRootDir.length + 1);//+1 skip the first '/'
        const parts = subPath.split(hostRequire('path').sep);
        if (parts.length < 4) {
            return;
        }


        const kEnvAndNameSize = 3;
        const [env, pluginName, grayEnvStr] = parts.slice(0, kEnvAndNameSize);
        const grayEnv = StringToGrayEnvMap[grayEnvStr];
        const pluginConfFile = this._getPluginConfFile(pluginName, grayEnv);
        const getTargetZipFile = () => {
            try {
                const plugin: LatestPluginInfo = JSON.parse(hostRequire("fs").readFileSync(pluginConfFile).toString());
                const targetZipFile = hostRequire('path').join(this._getPluginRootConfDir(pluginName, grayEnv), `${pluginName}-${plugin.buildTime}.zip`);
                return targetZipFile;
            } catch (e) {
            }
        }
        let targetZipFile = getTargetZipFile();
        if (!FileUtils.fileExist(targetZipFile)) {
            const pluginInfoList = await this._refreshLatestPluginList(grayEnv);
            const latestPluginInfo = pluginInfoList.find(item => pluginName === item.name);
            if (latestPluginInfo)
                await this._updatePlugin(latestPluginInfo, grayEnv);

            targetZipFile = getTargetZipFile();
        }

        if (FileUtils.fileExist(targetZipFile) && !FileUtils.fileExist(file)) {
            let entry = parts.slice(kEnvAndNameSize).join("/");
            if (entry.startsWith("/")) {
                entry = entry.substring(1);
            }
            const AdmZip = hostRequire('adm-zip');
            const zip = new AdmZip(targetZipFile);
            const pluginDir = this._getPluginRootDir(pluginName, grayEnv);
            zip.extractEntryTo(entry, pluginDir, true, true);
        }
    }

    public async loadAsyncLib(libName: string) {
       // 模块从缓存
       const cachedLib = this._nativeLibMap.get(libName);
       if(cachedLib) {
           return cachedLib;
       }

       // 先尝试一次加载
       // 用于初始化
       let nativeLibPath = await this.getPluginUrl(libName, true);

       // 再加载一次
       if(!nativeLibPath) {
           // 等待更新完成
           while(!nativeLibPath) {
              const grayEnv = abcHost.abcEnvironment.grayEnv();
              nativeLibPath = this.getPluginEntryUrl(libName, grayEnv);
              await delay(300);
           }
       }
       // 还是没有
       if(!nativeLibPath) {
           return null;
       }
       // 存储
       const load = hostRequire(nativeLibPath).default;
       this._nativeLibMap.set(libName, load({
            arch: process.arch,
            platform: process.platform,
            electronVersion: process.versions.electron,
       }));
       return this._nativeLibMap.get(libName);
    }
}

const pluginManager = new PluginManager();
export {pluginManager}
