/**
 * @description 用来热更新resource/目录下代码，实现热更新
 * <AUTHOR>
 */
import {FileUtils, logger, Version} from "../common";
import {UpgradeInfo} from "./upgrade-info";
import {LatestConfInfo, UpdaterBase} from "./updater-base";
import {abcHost, hostRequire} from "../../abc-host";

const fs = ()=>abcHost.hostRequire('fs');

const kTAG = "ResourcesUpdater";


export class ResourcesUpdater extends UpdaterBase {
    isUpdating: boolean = false;

    private latestInfo!: LatestConfInfo;

    constructor() {
        super();
    }

    /**
     * @return {Promise<boolean>} true，表示更新完成
     */
    async checkAndUpdate(): Promise<UpgradeInfo | undefined> {
        logger.log(kTAG, "开始检查资源更新...")
        if (!this.isPackaged) {
            logger.log(
                kTAG, `no need update for dev mode`);
            return;
        }

        const apiURL = this.getApiUrlWithAppId('abcyun-desktop-pc-asar');
        const latestInfoStr = await abcHost.networkUtils.getAsStringAutoCookie(apiURL);
        logger.log(`latestInfoStr = ${latestInfoStr}`);

        /**
         * @type {LatestConfInfo}
         */
        const latestInfo = JSON.parse(latestInfoStr)?.data;
        this.latestInfo = latestInfo;
        const currentVersion = abcHost.version
        logger.log(kTAG, `Asar 资源最新信息 latestInfo = ${JSON.stringify(latestInfo)}`);
        if (!this.latestInfo) {
            logger.log(kTAG, "无需更新，获取版本信息失败");
            return;
        }

        if (!this.latestInfo.version || new Version(this.latestInfo.version).compareTo(currentVersion) <= 0) {
            logger.log(kTAG, "无需更新，当前客户端最新版本");
            return;
        }

        logger.log(kTAG, `发现新的asar版本: ${latestInfo.version} `);
        await this.download(false);

        return {
            version: latestInfo.version,
            releaseNotes: latestInfo.description,
            updateType: "jsbundle"
        }
    }

    /**
     * 是否是打包后运行
     */
    get isPackaged(): boolean {
        return true;
        // return app.isPackaged === true;
    }

    async upgrade() {
        logger.info("abc-exe-upgrade");
        super.showUpdateInfo();
    }


    protected async download(autoInstall: boolean) {
        await this.doDownload({
            url: this.latestInfo.url,
            md5: this.latestInfo.md5,
            file: this._tmpAsarZip(),
            autoInstall: autoInstall
        })
    }

    private _tmpAsarZip() {
        return `${abcHost.pathUtils.tmpDir()}/resources-asar.zip`;
    }

    protected async install() {
        logger.log(`安装更新asar`);
        this.isUpdating = true;
        const tmpTargetResourcesDir = abcHost.pathUtils.getTmpResourcesDir();


        const tmpFile = this._tmpAsarZip();
        const AdmZip = abcHost.hostRequire('adm-zip');
        const zip = new AdmZip(tmpFile);
        zip.extractAllTo(tmpTargetResourcesDir, true);

        const tmpAsarUpdater = `${tmpTargetResourcesDir}/bin/asar-updater.exe`;

        // 获取升级程序asar-updater.exe的路径
        const asarUpdateExe = 'asar-updater.exe';
        let exePath = '';
        const path = hostRequire('path')
        if (abcHost.electron.app.isPackaged) {
            exePath = `${path.resolve(path.dirname(process.execPath), asarUpdateExe)}`;
        } else {
            exePath = `${path.resolve(`./bin/${asarUpdateExe}}`)}`;
        }

        //如果已经存在了exePath，则删除

        if (FileUtils.fileExist(exePath)) {
            try {
                FileUtils.deleteFileSync(exePath);
            } catch (ignored) {
            }
        }

        //将tmpAsarUpdate复制到exePath处，使用更新包里的升级程序
        fs().copyFileSync(tmpAsarUpdater, exePath);

        try {
            //执行更新程序进行更新替换操作
            abcHost.hostRequire('child_process').spawn(exePath, [
                `--launchExe=${process.execPath}`, //更新程序将会在更新完成后重新执行本程序
                `--dataDir=${abcHost.electron.app.getPath('userData')}`,//更新程序将会更新完成后将abc-conf.ini复制到该目录下
                `--killPrefixPathExeBeforeUpdate=${abcHost.hostRequire('path').dirname(process.execPath)}`,//在更新之前需要杀死指定路径下启动的进程
            ], {
                detached: true,
                stdio: "ignore",
                cwd: process.cwd() //设置当前工作目录，以便asar-update.bat脚本可以正确执行
            });

        } catch (e) {
            throw e;
        }
    }
}
