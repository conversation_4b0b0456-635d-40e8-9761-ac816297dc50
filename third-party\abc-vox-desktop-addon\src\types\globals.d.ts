export {}; // Make this a module

declare global {


    /*-----------------------------------------------*
    *                                               *
    *                   GLOBAL                      *
    *                                               *
    ------------------------------------------------*/

    // For backwards compability
    interface NodeRequire extends NodeJS.Require {}
    interface RequireResolve extends NodeJS.RequireResolve {}
    interface NodeModule extends NodeJS.Module {}

    var process: NodeJS.Process;
    var console: Console;

    var __filename: string;
    var __dirname: string;

    var require: any;
    var module: any;

    // Same as module.exports
    var exports: any;
}
