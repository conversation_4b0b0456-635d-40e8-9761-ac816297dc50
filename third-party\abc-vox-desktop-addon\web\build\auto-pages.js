const fs = require('fs');
const path = require('path');
const { resolve, createHtmlWebpackPlugin } = require('./helper');

/**
 * 自动扫描pages目录，生成多页应用的入口和HTML插件配置
 * 类似Nuxt.js的自动路由生成机制
 */
class AutoPagesGenerator {
    constructor(options = {}) {
        this.pagesDir = options.pagesDir || resolve('src/pages');
        this.templateFile = options.templateFile || 'template.html';
        this.entryFileName = options.entryFileName || 'index.js';
        this.exclude = options.exclude || [];
    }

    /**
     * 扫描pages目录，获取所有页面
     */
    scanPages() {
        if (!fs.existsSync(this.pagesDir)) {
            console.warn(`Pages directory not found: ${this.pagesDir}`);
            return [];
        }

        const pages = [];
        const items = fs.readdirSync(this.pagesDir);

        for (const item of items) {
            const itemPath = path.join(this.pagesDir, item);
            const stat = fs.statSync(itemPath);

            // 只处理目录
            if (!stat.isDirectory()) {
                continue;
            }

            // 跳过排除的目录
            if (this.exclude.includes(item)) {
                continue;
            }

            // 检查是否存在入口文件
            const entryFile = path.join(itemPath, this.entryFileName);
            if (!fs.existsSync(entryFile)) {
                console.warn(`Entry file not found for page "${item}": ${entryFile}`);
                continue;
            }

            // 检查是否有自定义配置文件
            const configFile = path.join(itemPath, 'page.config.js');
            let pageConfig = {};
            if (fs.existsSync(configFile)) {
                try {
                    pageConfig = require(configFile);
                } catch (error) {
                    console.warn(`Failed to load config for page "${item}":`, error.message);
                }
            }

            pages.push({
                name: item,
                path: itemPath,
                entryFile,
                config: pageConfig
            });
        }

        return pages;
    }

    /**
     * 生成rspack entry配置
     */
    generateEntries() {
        const pages = this.scanPages();
        const entries = {};

        for (const page of pages) {
            const entryKey = page.config.entryName || page.name;
            entries[entryKey] = resolve(`src/pages/${page.name}/${this.entryFileName}`);
        }

        return entries;
    }

    /**
     * 生成HTML插件配置
     */
    generateHtmlPlugins() {
        const pages = this.scanPages();
        const plugins = [];

        for (const page of pages) {
            const entryName = page.config.entryName || page.name;
            const filename = page.config.filename || `${page.name}.html`;
            
            // 统一使用template.html模板
            const template = this.templateFile;

            const htmlPlugin = createHtmlWebpackPlugin({
                filename,
                template,
                chunks: [entryName],
                ...page.config.htmlOptions
            });

            plugins.push(htmlPlugin);
        }

        return plugins;
    }
}

// 创建默认实例
const autoPagesGenerator = new AutoPagesGenerator();

module.exports = {
    autoPagesGenerator
};
