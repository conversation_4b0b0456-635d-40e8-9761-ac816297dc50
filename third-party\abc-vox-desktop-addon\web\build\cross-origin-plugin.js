module.exports = class AbcCrossOriginPlugin {
    constructor (options) {
        this.options = options || { crossorigin: 'anonymous' }
    }

    apply (compiler) {
        const HtmlWebpackPlugin = require('html-webpack-plugin');
        compiler.hooks.compilation.tap('AbcCrossOriginPlugin', (compilation) => {
            HtmlWebpackPlugin
                .getHooks(compilation)
                .alterAssetTags
                .tapAsync('AbcCrossOriginPlugin', (htmlPluginData, callback) => {
                    htmlPluginData.assetTags.scripts.forEach(item => {
                        item.attributes.crossorigin = this.options.crossorigin;
                    })
                    callback(null, htmlPluginData)
                })
        })
    }
}
