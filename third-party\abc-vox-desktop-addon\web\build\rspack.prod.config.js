const rspack = require('@rspack/core');
const { getPublicPath, AbcWebpackOSSPlugin, getOSSUploadInfo } = require('./helper');
const AbcCrossOriginPlugin = require('./cross-origin-plugin');
const CssMinimizerWebpackPlugin = require('css-minimizer-webpack-plugin');
const { merge } = require('webpack-merge');
const baseConfig = require('./rspack.base.config')

module.exports = merge(baseConfig, {
    mode: 'production',
    cache: true,
    devtool: 'hidden-source-map',
    output: {
        publicPath: getPublicPath('vox')
    },
    module: {
        rules: [
            {
                test: /\.css$/,
                use: [
                    {
                        loader: rspack.CssExtractRspackPlugin.loader,
                        parallel: true,
                    },
                    { loader: 'css-loader', options: { sourceMap: false } },
                    {
                        "loader": "postcss-loader",
                        "options": {
                            "sourceMap": false
                        }
                    },
                ]
            },
            {
                test: /\.scss$/,
                use: [
                    {
                        loader: rspack.CssExtractRspackPlugin.loader,
                        parallel: true,
                    },
                    { loader: 'css-loader', options: { sourceMap: false } },
                    {
                        "loader": "postcss-loader",
                        "options": {
                            "sourceMap": false
                        }
                    },
                    {
                        loader: 'sass-loader',
                        options: {
                            sourceMap: false
                        }
                    }
                ]
            }
        ]
    },
    plugins: [
        new rspack.CssExtractRspackPlugin({
            filename: 'css/[name].[contenthash].css',
            ignoreOrder: true,
        }),

        new AbcCrossOriginPlugin(),

        new AbcWebpackOSSPlugin({
            prefix: `vox`,
            ...getOSSUploadInfo(),
            map: true,
            buildTag: process.env.BUILD_TAG,
            buildEnv: process.env.BUILD_ENV,
            buildNumber: process.env.BUILD_NUMBER,
        })
    ],
    optimization: {
        splitChunks: {
            chunks: 'all',
            minSize: 20000, // 20kb
            maxInitialRequests: 12,
            cacheGroups: {
                abcUI: {
                    name: 'chunk-abcui',
                    chunks: 'all',
                    test: /[\\/]node_modules[\\/](@abc\/ui-pc)[\\/]/,
                    priority: 20,
                    reuseExistingChunk: true
                },
                lib: {
                    test(module) {
                        return (
                            module.type !== 'css/mini-extract' &&
                            module.size() > 160000 &&
                            /node_modules[/\\]/.test(module.nameForCondition() || '')
                        )
                    },
                    name(module) {
                        const packageNameArr = module.context.match(/[\\/]node_modules[\\/](.*?)([\\/]|$)/);
                        const packageName = packageNameArr ? packageNameArr[1] : '';
                        return `chunk-lib.${packageName.replace("@", "")}`;
                    },
                    priority: 15,
                    minChunks: 2,
                    reuseExistingChunk: true,
                },
                vendors: {
                    name: 'chunk-vendors',
                    test: /[\\/]node_modules[\\/]/,
                    priority: 10,
                    chunks: 'initial'
                },
                commons: {
                    name: 'chunk-commons',
                    minChunks: 5, // minimum common number
                    priority: 0,
                    maxSize: 4000000,
                    reuseExistingChunk: true,
                },
            },
        },
        runtimeChunk: {
            name: 'runtime'
        },
        minimize: true,
        minimizer: [
            new rspack.SwcJsMinimizerRspackPlugin({
                minimizerOptions: {
                    format: {
                        comments: false,
                    },
                },
            }),

            new CssMinimizerWebpackPlugin(),
        ],
    },
    experiments: {
        css: false,
        parallelLoader: true,
        parallelCodeSplitting: false,
    }
})