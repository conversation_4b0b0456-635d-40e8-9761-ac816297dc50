rem @echo off

cd ..


set ELECTRON_CACHE=electron-tmp-cache

@REM git clean -fd
@REM git co .
@REM rem git pull --rebase
@REM rd /q /s dist
@REM rd /q /s node_modules
@REM rd /q /s third-party\app-builder-lib\node_modules
@REM rd /q /s third-party\electron-builder\node_modules
@REM rd /q /s third-party\abc-vox-desktop-addon\node_modules
@REM rd /q /s third-party\abc-vox-desktop-addon\dist
@REM rd /q /s %ELECTRON_CACHE%
@REM mkdir %ELECTRON_CACHE%

@REM echo UPLOAD_OSS=%UPLOAD_OSS%

for /F %%i in ('node -v') do ( set NodeVersion=%%i)
echo node version: %NodeVersion%

echo ELECTRON_MIRROR = %ELECTRON_MIRROR%
echo ELECTRON_BUILDER_CACHE = %ELECTRON_BUILDER_CACHE%

for /F %%i in ('npm config get registry') do ( set NpmRegistry=%%i)
echo npm registry=%NpmRegistry%

for /F %%i in ('node buildscripts\get-package-info.js') do ( set APPVERSION=%%i)

if "%time:~0,2%" lss "10" (set hh=0%time:~1,1%) else (set hh=%time:~0,2%)
echo %hh%:%time:~3,2%
SET TimeStamp=%date:~0,4%%date:~5,2%%date:~8,2%%hh%%time:~3,2%%time:~6,2%
echo TimeStamp=%TimeStamp%


for /f "delims=" %%i in ('git rev-parse HEAD') do set COMMIT=%%i
echo const BuildConfig = {> src/build-config.ts
echo 	gitCommit:"%COMMIT%", >> src/build-config.ts
echo 	buildTime:"%TimeStamp%" >> src/build-config.ts
echo };>> src/build-config.ts

echo export {BuildConfig};>> src/build-config.ts

set BUILD_TAG=%COMMIT%

echo current pwd =%cd%
@REM set force_no_cache=true
@REM call npm ci
@REM cd third-party\abc-vox-desktop-addon
@REM call npm ci
@REM cd ..\..
@REM echo third-party\abc-vox-desktop-addon npm ci done
@REM echo current pwd =%cd%


call npm run dist-win-32
@REM echo npm run dist-win-32 done

@REM copy /y dist\abcyun-desktop-win-%APPVERSION%.exe dist\abcyun-desktop-win-%APPVERSION%_%TimeStamp%.exe
@REM copy /y dist\abcyun-desktop-win-%APPVERSION%.exe dist\abcyun-desktop-win-latest.exe
@REM copy /y dist\abcyun-desktop-win-%APPVERSION%.exe dist\abcyun-desktop-win-print-service-latest.exe


@REM if "%UPLOAD_OSS%"=="true" (call npm run uploadCI)
@REM if "%UPLOAD_TOLATEST%"=="true" (call npm run uploadReleaseToLatest)
@REM if "%UPLOAD_TOLATEST_EXT_LIB%"=="true" (call npm run uploadPackDevExtLibs)
@REM if "%UPLOAD_TOLATEST_EXT_DLL%"=="true" (call npm run uploadPackExtDlls)
@REM pause
