cd ..

rm -rf dist
rm -rf node_modules
rm -rf third-party/app-builder-lib/node_modules
rm -rf third-party/electron-builder/node_modules
rm -rf src/native-lib/build
rm -rf third-party/sqlite3/build
rm -rf third-party/sqlite3/build-tmp-napi-v6
rm -rf third-party/sqlite3/build-tmp-napi-v8
rm -rf third-party/abc-vox-desktop-addon/dist
rm -rf third-party/abc-vox-desktop-addon/node_modules


cp -f package_for_mac.json package.json

TimeStamp=`date +"%Y%m%d%H%M%S"`
COMMIT=`git rev-parse HEAD`

echo const BuildConfig = {> src/build-config.ts
echo  "\tgitCommit:\"$COMMIT\"", >> src/build-config.ts
echo 	"\tbuildTime:\"$TimeStamp\"" >> src/build-config.ts
echo "};">> src/build-config.ts

echo "export {BuildConfig};">> src/build-config.ts

export set BUILD_TAG=$COMMIT

npm install
cd third-party/abc-vox-desktop-addon
npm install
cd ../..

npm run dist-mac
node buildscripts/notarize.js
